import { getCollection } from 'astro:content';

// Define the return type for getChapterInfo
export interface ChapterInfo {
  guideId: string;
  guideSlug: string;
  guideTitle: string;
  currentChapter: number;
  totalChapters: number;
  chapters: Array<{
    title: string;
    url: string;
    isActive?: boolean;
  }>;
  prevChapter: {
    title: string;
    url: string;
  } | null;
  nextChapter: {
    title: string;
    url: string;
  } | null;
}

/**
 * Get all guides from the content collection
 */
export async function getAllGuides() {
  return await getCollection('guide');
}

/**
 * Get a specific guide by its slug
 */
export async function getGuideBySlug(slug: string) {
  const guides = await getAllGuides();
  return guides.find(guide => guide.data.slug === slug);
}

/**
 * Get chapter information for a post
 * This function finds which guide a post belongs to and returns information about the chapter
 */
export async function getChapterInfo(postSlug: string): Promise<ChapterInfo | null> {
  const guides = await getAllGuides();

  for (const guide of guides) {
    const chapterIndex = guide.data.chapters.findIndex(
      chapter => chapter.slug === postSlug
    );

    if (chapterIndex >= 0) {
      return {
        guideId: guide.id,
        guideSlug: guide.data.slug,
        guideTitle: guide.data.title,
        currentChapter: chapterIndex + 1,
        totalChapters: guide.data.chapters.length,
        chapters: guide.data.chapters.map((chapter, index) => ({
          title: chapter.title,
          url: `/${chapter.slug}/`,
          isActive: index === chapterIndex
        })),
        prevChapter: chapterIndex > 0
          ? {
              title: guide.data.chapters[chapterIndex - 1].title,
              url: `/${guide.data.chapters[chapterIndex - 1].slug}/`
            }
          : null,
        nextChapter: chapterIndex < guide.data.chapters.length - 1
          ? {
              title: guide.data.chapters[chapterIndex + 1].title,
              url: `/${guide.data.chapters[chapterIndex + 1].slug}/`
            }
          : null
      };
    }
  }

  return null;
}

/**
 * Check if a post is part of any guide
 * This is a more flexible approach than checking for a specific tag
 */
export async function isGuidePost(postSlug: string): Promise<boolean> {
  const chapterInfo = await getChapterInfo(postSlug);
  return chapterInfo !== null;
}
