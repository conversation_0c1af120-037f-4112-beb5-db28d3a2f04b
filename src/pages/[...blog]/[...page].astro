---
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro';

import Layout from '~/layouts/PageLayout.astro';
import BlogGrid from '~/components/blog/BlogGrid.astro';
import FeaturedPosts from '~/components/blog/FeaturedPosts.astro';
import CategorySection from '~/components/blog/CategorySection.astro';
import BlogFilter from '~/components/blog/BlogFilter.astro';
import Pagination from '~/components/blog/Pagination.astro';
import { blogListRobots, getStaticPathsBlogList, fetchPosts } from '~/utils/blog';

export const prerender = true;

export const getStaticPaths = (async ({ paginate }) => {
  return await getStaticPathsBlogList({ paginate });
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { page } = Astro.props as Props;
const currentPage = page.currentPage ?? 1;

// Get all posts for featured sections and category sections
const allPosts = await fetchPosts();

/**
 * Artikel Unggulan (Featured Posts) Logic
 *
 * Artikel unggulan ditentukan berdasarkan prioritas berikut:
 * 1. Post dengan tag 'featured' - Tambahkan tag 'featured' ke frontmatter post untuk menandainya sebagai unggulan
 * 2. Post dengan metadata.featured = true - Tambahkan property 'featured: true' ke metadata di frontmatter
 * 3. Post dari kategori penting (panduan, review-hosting) yang terbaru
 * 4. Post terbaru jika tidak ada post yang memenuhi kriteria di atas
 *
 * Jumlah artikel unggulan: 4 artikel (1 highlighted + 3 regular)
 */

// 1. First priority: Posts with 'featured' tag
const taggedFeaturedPosts = allPosts
  .filter(post => post.tags?.some(tag => tag.slug === 'featured'));

// 2. Second priority: Posts with metadata.featured = true
const metadataFeaturedPosts = allPosts
  .filter(post => post.metadata?.featured === true &&
    !taggedFeaturedPosts.some(p => p.id === post.id)); // Avoid duplicates

// 3. Third priority: Important category posts (newest first)
const importantCategoryPosts = allPosts
  .filter(post =>
    (post.category?.slug === 'panduan' ||
     post.category?.slug === 'review-hosting') &&
    !taggedFeaturedPosts.some(p => p.id === post.id) &&
    !metadataFeaturedPosts.some(p => p.id === post.id)
  )
  .sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime());

// 4. Fourth priority: Newest posts
const newestPosts = allPosts
  .filter(post =>
    !taggedFeaturedPosts.some(p => p.id === post.id) &&
    !metadataFeaturedPosts.some(p => p.id === post.id) &&
    !importantCategoryPosts.some(p => p.id === post.id)
  )
  .sort((a, b) => new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime());

// Combine all featured posts based on priority
const allFeaturedPosts = [
  ...taggedFeaturedPosts,
  ...metadataFeaturedPosts,
  ...importantCategoryPosts,
  ...newestPosts
];

// Take the first 4 posts for the featured section
const effectiveFeaturedPosts = allFeaturedPosts.slice(0, 4);

// Count posts by type for filter badges
const guidePageCount = allPosts.filter(post =>
  post.metadata?.layout === 'guide-page' ||
  post.metadata?.isGuidePage === true ||
  post.category?.slug === 'panduan' ||
  post.category?.title === 'Panduan'
).length;

const reviewCount = allPosts.filter(post =>
  post.category?.slug === 'review-hosting'
).length;

const tutorialCount = allPosts.filter(post =>
  post.category?.slug === 'tutorial' ||
  post.tags?.some(tag => tag.slug === 'tutorial')
).length;

// Create post type filters with counts
const postTypes = [
  { slug: 'all', title: 'Semua', icon: 'tabler:article', count: allPosts.length },
  { slug: 'guide-page', title: 'Panduan', icon: 'tabler:book', count: guidePageCount },
  { slug: 'review-hosting', title: 'Review', icon: 'tabler:star', count: reviewCount },
  { slug: 'tutorial', title: 'Tutorial', icon: 'tabler:school', count: tutorialCount },
];

const metadata = {
  title: `Blog${currentPage > 1 ? ` — Page ${currentPage}` : ''}`,
  description: 'Artikel, tutorial, panduan, dan review hosting terbaik untuk membantu Anda membuat dan mengelola website.',
  robots: {
    index: blogListRobots?.index && currentPage === 1,
    follow: blogListRobots?.follow,
  },
  openGraph: {
    type: 'blog',
  },
};
---

<Layout metadata={metadata}>
  <section class="px-4 sm:px-6 py-12 sm:py-16 lg:py-20 mx-auto max-w-global">
    <div class="mb-8 text-center max-w-3xl mx-auto">
      <h1 class="text-4xl md:text-5xl font-bold leading-tighter tracking-tighter font-heading mb-4 dark:text-white">
        Blog & Artikel
      </h1>
      <p class="text-lg text-gray-600 dark:text-slate-400">
        Artikel, tutorial, panduan, dan review hosting terbaik untuk membantu Anda membuat dan mengelola website.
      </p>
    </div>

    {currentPage === 1 && (
      <>
        {/* Featured Posts Section */}
        <FeaturedPosts posts={effectiveFeaturedPosts} />

        {/* Category Sections */}
        <CategorySection
          title="Panduan Lengkap"
          posts={allPosts}
          category="panduan"
          icon="tabler:book"
        />

        <CategorySection
          title="Tutorial"
          posts={allPosts}
          category="tutorial"
          icon="tabler:school"
        />

        <CategorySection
          title="Review Hosting"
          posts={allPosts}
          category="review-hosting"
          icon="tabler:star"
        />

        <div class="border-t border-gray-200 dark:border-gray-800 my-12 pt-12">
          <h2 class="text-3xl font-bold mb-8 text-center dark:text-white">Semua Artikel</h2>
        </div>
      </>
    )}

    {currentPage === 1 ? (
      <>
        {/* Sections with sidebar filter */}
        <div class="grid md:grid-cols-4 gap-8 md:gap-12 mb-16">
          <div class="md:col-span-1">
            <div class="sticky top-24">
              <BlogFilter postTypes={postTypes} />
            </div>
          </div>

          <div class="md:col-span-3">
            {/* Featured Posts and Category Sections are rendered above */}
            {/* This section is empty because we don't want to show posts here */}
          </div>
        </div>

        {/* Full-width "Semua Artikel" section */}
        <div>
          <BlogGrid
            posts={page.data}
            layout="grid"
            className="grid-cols-1 md:grid-cols-3 lg:grid-cols-3"
          />

          <div class="mt-12">
            <Pagination prevUrl={page.url.prev} nextUrl={page.url.next} />
          </div>
        </div>
      </>
    ) : (
      <div>
        <BlogGrid
          posts={page.data}
          layout="grid"
          className="grid-cols-1 md:grid-cols-3 lg:grid-cols-3"
        />

        <div class="mt-12">
          <Pagination prevUrl={page.url.prev} nextUrl={page.url.next} />
        </div>
      </div>
    )}
  </section>
</Layout>
