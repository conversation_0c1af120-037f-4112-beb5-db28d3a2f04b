---
import type { InferGetStaticPropsType, GetStaticPaths } from 'astro';
import merge from 'lodash.merge';
import type { ImageMetadata } from 'astro';
import Layout from '~/layouts/PageLayout.astro';
import SinglePost from '~/components/blog/SinglePost.astro';
import TwoColumnPost from '~/components/blog/TwoColumnPost.astro';
import GuideLayout from '~/components/blog/GuideLayout.astro';
import GuidePageLayout from '~/components/blog/GuidePageLayout.astro';
import { getCanonical, getPermalink } from '~/utils/permalinks';
import { getStaticPathsBlogPost, blogPostRobots } from '~/utils/blog';
import { findImage } from '~/utils/images';
import { getChapterInfo, isGuidePost as checkIsGuidePost } from '~/utils/guides';
import type { MetaData } from '~/types';
import RelatedPosts from '~/components/blog/RelatedPosts.astro';

export const prerender = true;

export const getStaticPaths = (async () => {
  return await getStaticPathsBlogPost();
}) satisfies GetStaticPaths;

type Props = InferGetStaticPropsType<typeof getStaticPaths>;

const { post } = Astro.props as Props;
const isReviewHosting = post.category?.slug === 'review-hosting';

// Get notice information from metadata if available
const noticeType = post.metadata?.noticeType || null;
const noticeDate = post.metadata?.noticeDate || post.updateDate || post.publishDate;

const url = getCanonical(getPermalink(post.permalink, 'post'));
const image = (await findImage(post.image)) as ImageMetadata | string | undefined;

// Check if this post is part of a guide using our new flexible approach
import type { ChapterInfo } from '~/utils/guides';
let guideInfo: ChapterInfo | null = null;
const isGuidePost = await checkIsGuidePost(post.slug);
if (isGuidePost) {
  guideInfo = await getChapterInfo(post.slug);
}

// Check if this is a single-page guide (contains multiple chapters in one page)
// We can detect this by checking metadata or tags
const isSinglePageGuide = post.metadata?.layout === 'guide-page' ||
                         post.metadata?.isGuidePage === true ||
                         (post.tags && post.tags.some(tag =>
                           ['panduan-lengkap', 'guide', 'tutorial-lengkap'].includes(tag.slug)
                         ));

const metadata = merge(
  {
    title: post.title,
    description: post.excerpt,
    robots: {
      index: blogPostRobots?.index,
      follow: blogPostRobots?.follow,
    },
    openGraph: {
      type: 'article',
      ...(image
        ? { images: [{ url: image, width: (image as ImageMetadata)?.width, height: (image as ImageMetadata)?.height }] }
        : {}),
    },
  },
  { ...(post?.metadata ? { ...post.metadata, canonical: post.metadata?.canonical || url } : {}) }
) as MetaData;
---

<Layout metadata={metadata}>
  {isReviewHosting ? (
    <TwoColumnPost
      post={{ ...post, image: image }}
      url={url}
      noticeType={noticeType}
      noticeDate={noticeDate}
    >
      {post.Content ? <post.Content /> : <Fragment set:html={post.content || ''} />}
    </TwoColumnPost>
  ) : isGuidePost && guideInfo ? (
    <GuideLayout
      post={{ ...post, image: image }}
      url={url}
      noticeType={noticeType}
      noticeDate={noticeDate}
      chapters={guideInfo.chapters}
      currentChapter={guideInfo.currentChapter}
      totalChapters={guideInfo.totalChapters}
    >
      {post.Content ? <post.Content /> : <Fragment set:html={post.content || ''} />}
    </GuideLayout>
  ) : isSinglePageGuide ? (
    <GuidePageLayout
      post={{ ...post, image: image }}
      url={url}
      noticeType={noticeType}
      noticeDate={noticeDate}
      showNotice={post.metadata?.showNotice !== false}
    >
      {post.Content ? <post.Content /> : <Fragment set:html={post.content || ''} />}
    </GuidePageLayout>
  ) : (
    <SinglePost post={{ ...post, image: image }} url={url}>
      {post.Content ? <post.Content /> : <Fragment set:html={post.content || ''} />}
    </SinglePost>
  )}

  {/* <ToBlogLink /> Dihapus dari sini karena sudah ada di dalam TwoColumnPost */}
  {!isGuidePost && post.slug !== 'cara-membuat-website' && <RelatedPosts post={post} />}
</Layout>
