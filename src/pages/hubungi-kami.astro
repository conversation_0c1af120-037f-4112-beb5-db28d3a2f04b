---
import Layout from '~/layouts/PageLayout.astro';
import CustomStyles from '~/components/CustomStyles.astro';

const metadata = {
  title: '<PERSON><PERSON><PERSON><PERSON> - Penasihat Hosting',
  description: '<PERSON><PERSON><PERSON><PERSON> tim Penasihat Hosting untuk pertanyaan seputar web hosting, domain, dan pembuatan website. Email: <EMAIL>',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/default-og-image.webp', // Using a default image URL
      }
    ]
  }
};
---

<Layout metadata={metadata}>
  <CustomStyles />

  <!-- Hero Section -->
  <section class="py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 text-center">
      <h1 class="text-5xl font-bold leading-tight text-[var(--aw-color-text-heading)] tracking-tight">
        Hubungi <span class="italic"><PERSON><PERSON></span>
      </h1>
      <p class="mt-4 leading-relaxed">
        Tim <PERSON>hat Hosting siap membantu Anda dengan pertanyaan seputar web hosting, domain, dan pembuatan website.
      </p>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="py-8">
    <div class="max-w-3xl mx-auto px-5 sm:px-6">
      <article class="prose dark:prose-invert max-w-none">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 mb-8">
          <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600 dark:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h2 class="text-2xl font-semibold text-[var(--aw-color-text-heading)] mb-4">Kirim Email</h2>
            <p class="mb-6">
              Untuk pertanyaan dan informasi lebih lanjut, silakan hubungi kami melalui email di:
            </p>
            <a href="mailto:<EMAIL>" class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition duration-300">
              <EMAIL>
            </a>
          </div>
        </div>

        <div class="space-y-12">
          <div>
            <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-6">Pertanyaan Umum</h2>
            <ul class="space-y-4">
              <li class="flex items-start">
                <svg class="h-6 w-6 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Pertanyaan tentang web hosting dan domain</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Panduan pemilihan hosting yang tepat</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Informasi umum tentang pembuatan website</span>
              </li>
            </ul>
          </div>

          <div>
            <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-6">Mohon Perhatikan</h2>
            <ul class="space-y-4">
              <li class="flex items-start">
                <svg class="h-6 w-6 text-red-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span>Karena keterbatasan, tidak semua pertanyaan mungkin dapat kami jawab.</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-red-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span>Kami tidak menerima pertanyaan di luar topik hosting, domain, dan pembuatan website.</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-red-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span>Kami tidak dapat membantu dengan pertanyaan terkait website ilegal atau konten yang melanggar hukum.</span>
              </li>
              <li class="flex items-start">
                <svg class="h-6 w-6 text-blue-500 mr-3 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Untuk masalah teknis spesifik dengan provider hosting tertentu, sebaiknya hubungi langsung tim support provider tersebut.</span>
              </li>
            </ul>
          </div>
        </div>
      </article>
    </div>
  </section>
</Layout>
