---
import Layout from '~/layouts/PageLayout.astro';
import { Icon } from 'astro-icon/components';

const metadata = {
  title: 'Kalkulator Uptime - Hitung Downtime dari Persentase Uptime | Penasihat Hosting',
  description: 'Kalkulator uptime untuk menghitung waktu downtime berdasarkan persentase uptime. Ketahui berapa lama website Anda akan offline dalam setahun, sebulan, seminggu, atau sehari.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/kalkulator-uptime.webp',
      }
    ]
  }
};
---

<Layout metadata={metadata}>
  <!-- Hero Section -->
  <section class="bg-gradient-to-b from-blue-50 to-white dark:from-slate-900 dark:to-slate-800 py-12 md:py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <div class="flex justify-center mb-6">
          <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full">
            <Icon name="tabler:clock" class="w-12 h-12 text-primary" />
          </div>
        </div>
        <h1 class="text-4xl md:text-5xl font-bold text-[var(--aw-color-text-heading)] mb-6">
          Kalkulator Uptime
        </h1>
        <p class="text-xl text-[var(--aw-color-text-muted)] max-w-3xl mx-auto mb-8">
          Hitung waktu downtime berdasarkan persentase uptime hosting Anda. Ketahui berapa lama website akan offline dalam periode waktu tertentu.
        </p>

        <!-- Quick Preset Buttons -->
        <div class="flex flex-wrap justify-center gap-3 mb-8">
          <button class="preset-btn" data-value="99">99%</button>
          <button class="preset-btn" data-value="99.9">99.9%</button>
          <button class="preset-btn" data-value="99.99">99.99%</button>
          <button class="preset-btn" data-value="99.999">99.999%</button>
        </div>
      </div>
    </div>
  </section>

  <!-- Calculator Section -->
  <section class="py-12 md:py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid lg:grid-cols-2 gap-12">
        <!-- Calculator Form -->
        <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700 p-8">
          <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-6 flex items-center">
            <Icon name="tabler:calculator" class="w-6 h-6 mr-3 text-primary" />
            Masukkan Persentase Uptime
          </h2>

          <form id="uptimeForm" class="space-y-6">
            <div>
              <label for="uptimeInput" class="block text-sm font-medium text-[var(--aw-color-text-default)] mb-2">
                Persentase Uptime (%)
              </label>
              <div class="relative">
                <input
                  type="number"
                  id="uptimeInput"
                  name="uptime"
                  min="0"
                  max="100"
                  step="0.001"
                  placeholder="99.9"
                  class="w-full px-4 py-3 text-lg border border-gray-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-700 text-[var(--aw-color-text-default)] focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                />
                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span class="text-[var(--aw-color-text-muted)] text-lg">%</span>
                </div>
              </div>
              <div id="errorMessage" class="mt-2 text-sm text-red-600 dark:text-red-400 hidden"></div>
            </div>

            <button
              type="submit"
              class="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
            >
              <Icon name="tabler:calculator" class="w-5 h-5 mr-2" />
              Hitung Downtime
            </button>
          </form>

          <!-- Additional Info -->
          <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div class="flex items-start">
              <Icon name="tabler:info-circle" class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
              <div class="text-sm text-blue-800 dark:text-blue-200">
                <p class="font-semibold mb-1">Tips:</p>
                <ul class="space-y-1 text-blue-700 dark:text-blue-300">
                  <li>• Masukkan nilai antara 0-100%</li>
                  <li>• Gunakan titik (.) untuk desimal</li>
                  <li>• Contoh: 99.9 atau 99.99</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Results Display -->
        <div id="resultsContainer" class="hidden">
          <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700 p-8">
            <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-6 flex items-center">
              <Icon name="tabler:clock-exclamation" class="w-6 h-6 mr-3 text-red-500" />
              Waktu Downtime
            </h2>

            <div class="grid grid-cols-2 gap-4">
              <!-- Per Year -->
              <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div class="text-center">
                  <Icon class="w-8 h-8 text-red-600 dark:text-red-400 mx-auto mb-2" />
                  <h3 class="font-semibold text-red-800 dark:text-red-200 mb-1">Per Tahun</h3>
                  <p id="yearlyDowntime" class="text-lg font-bold text-red-600 dark:text-red-400">-</p>
                </div>
              </div>

              <!-- Per Month -->
              <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                <div class="text-center">
                  <Icon name="tabler:calendar-month" class="w-8 h-8 text-orange-600 dark:text-orange-400 mx-auto mb-2" />
                  <h3 class="font-semibold text-orange-800 dark:text-orange-200 mb-1">Per Bulan</h3>
                  <p id="monthlyDowntime" class="text-lg font-bold text-orange-600 dark:text-orange-400">-</p>
                </div>
              </div>

              <!-- Per Week -->
              <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="text-center">
                  <Icon name="tabler:calendar-week" class="w-8 h-8 text-yellow-600 dark:text-yellow-400 mx-auto mb-2" />
                  <h3 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-1">Per Minggu</h3>
                  <p id="weeklyDowntime" class="text-lg font-bold text-yellow-600 dark:text-yellow-400">-</p>
                </div>
              </div>

              <!-- Per Day -->
              <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="text-center">
                  <Icon name="tabler:calendar" class="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                  <h3 class="font-semibold text-blue-800 dark:text-blue-200 mb-1">Per Hari</h3>
                  <p id="dailyDowntime" class="text-lg font-bold text-blue-600 dark:text-blue-400">-</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Information Section -->
  <section class="py-12 md:py-16 bg-gray-50 dark:bg-slate-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-[var(--aw-color-text-heading)] text-center mb-12">
        Memahami Uptime dan Downtime
      </h2>

      <div class="grid md:grid-cols-2 gap-8 mb-12">
        <!-- What is Uptime -->
        <div class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700">
          <div class="flex items-center mb-4">
            <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full mr-4">
              <Icon name="tabler:server" class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <h3 class="text-xl font-bold text-[var(--aw-color-text-heading)]">Apa itu Uptime?</h3>
          </div>
          <p class="text-[var(--aw-color-text-default)] leading-relaxed">
            Uptime adalah persentase waktu dimana server atau website Anda dapat diakses dan berfungsi normal.
            Uptime 99.9% berarti website Anda online 99.9% dari total waktu dalam periode tertentu.
          </p>
        </div>

        <!-- What is Downtime -->
        <div class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700">
          <div class="flex items-center mb-4">
            <div class="bg-red-100 dark:bg-red-900/30 p-3 rounded-full mr-4">
              <Icon name="tabler:server-off" class="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <h3 class="text-xl font-bold text-[var(--aw-color-text-heading)]">Apa itu Downtime?</h3>
          </div>
          <p class="text-[var(--aw-color-text-default)] leading-relaxed">
            Downtime adalah waktu dimana website atau server tidak dapat diakses oleh pengunjung.
            Semakin tinggi uptime, semakin sedikit downtime yang dialami website Anda.
          </p>
        </div>
      </div>

      <!-- Why It Matters -->
      <div class="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-slate-700">
        <div class="flex items-center mb-6">
          <div class="bg-primary/10 dark:bg-primary/20 p-3 rounded-full mr-4">
            <Icon name="tabler:trending-up" class="w-6 h-6 text-primary" />
          </div>
          <h3 class="text-2xl font-bold text-[var(--aw-color-text-heading)]">Mengapa Uptime Penting?</h3>
        </div>

        <div class="grid md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Icon name="tabler:users" class="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h4 class="font-semibold text-[var(--aw-color-text-heading)] mb-2">Pengalaman Pengguna</h4>
            <p class="text-sm text-[var(--aw-color-text-muted)]">Website yang sering down akan membuat pengunjung frustrasi dan beralih ke kompetitor</p>
          </div>

          <div class="text-center">
            <div class="bg-green-100 dark:bg-green-900/30 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Icon name="tabler:currency-dollar" class="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h4 class="font-semibold text-[var(--aw-color-text-heading)] mb-2">Kerugian Finansial</h4>
            <p class="text-sm text-[var(--aw-color-text-muted)]">Downtime dapat menyebabkan kehilangan penjualan dan revenue, terutama untuk e-commerce</p>
          </div>

          <div class="text-center">
            <div class="bg-purple-100 dark:bg-purple-900/30 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Icon name="tabler:search" class="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h4 class="font-semibold text-[var(--aw-color-text-heading)] mb-2">SEO Impact</h4>
            <p class="text-sm text-[var(--aw-color-text-muted)]">Search engine dapat menurunkan ranking website yang sering mengalami downtime</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Reference Table Section -->
  <section class="py-12 md:py-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-[var(--aw-color-text-heading)] text-center mb-12">
        Tabel Referensi Uptime
      </h2>

      <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50 dark:bg-slate-700">
              <tr>
                <th class="px-6 py-4 text-left text-xs font-medium text-[var(--aw-color-text-muted)] uppercase tracking-wider">Uptime</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-[var(--aw-color-text-muted)] uppercase tracking-wider">Downtime/Tahun</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-[var(--aw-color-text-muted)] uppercase tracking-wider">Downtime/Bulan</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-[var(--aw-color-text-muted)] uppercase tracking-wider">Kategori</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-slate-600">
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">90%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 dark:text-red-400">36.53 hari</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 dark:text-red-400">73 jam</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Poor</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">95%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-orange-600 dark:text-orange-400">18.26 hari</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-orange-600 dark:text-orange-400">36.5 jam</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Fair</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">99%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600 dark:text-yellow-400">3.65 hari</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600 dark:text-yellow-400">7.31 jam</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Good</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">99.9%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400">8.77 jam</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400">43.8 menit</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Very Good</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">99.99%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">52.6 menit</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">4.38 menit</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Excellent</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">99.999%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">5.26 menit</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">26.3 detik</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Excellent</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </section>
</Layout>

<style>
  .preset-btn {
    @apply px-3 py-2 text-sm font-medium text-primary border border-primary rounded-lg hover:bg-primary hover:text-white transition-colors duration-200;
  }

  .preset-btn.active {
    @apply bg-primary text-white;
  }
</style>

<script>
  // Uptime Calculator Logic
  class UptimeCalculator {
    // Explicit property declarations with proper types
    form: HTMLFormElement | null;
    input: HTMLInputElement | null;
    errorMessage: HTMLElement | null;
    resultsContainer: HTMLElement | null;
    presetButtons: NodeListOf<Element>;

    constructor() {
      this.form = document.getElementById('uptimeForm') as HTMLFormElement | null;
      this.input = document.getElementById('uptimeInput') as HTMLInputElement | null;
      this.errorMessage = document.getElementById('errorMessage') as HTMLElement | null;
      this.resultsContainer = document.getElementById('resultsContainer') as HTMLElement | null;
      this.presetButtons = document.querySelectorAll('.preset-btn');

      // Check if all required elements exist
      if (!this.form || !this.input || !this.errorMessage || !this.resultsContainer) {
        console.error('Required elements not found');
        return;
      }

      this.initEventListeners();
    }

    initEventListeners(): void {
      // Form submission
      this.form?.addEventListener('submit', (e: Event) => {
        e.preventDefault();
        this.calculateDowntime();
      });

      // Real-time calculation on input
      this.input?.addEventListener('input', () => {
        this.clearError();
        if (this.input?.value) {
          this.calculateDowntime();
        } else {
          this.hideResults();
        }
      });

      // Preset buttons
      this.presetButtons?.forEach((btn: Element) => {
        btn.addEventListener('click', () => {
          const value = (btn as HTMLElement).dataset.value;
          if (this.input && value) {
            this.input.value = value;
            this.updateActivePreset(btn as HTMLElement);
            this.calculateDowntime();
          }
        });
      });
    }

    updateActivePreset(activeBtn: HTMLElement): void {
      this.presetButtons?.forEach((btn: Element) => btn.classList.remove('active'));
      activeBtn?.classList.add('active');
    }

    validateInput(uptime: number): boolean {
      if (isNaN(uptime) || uptime < 0 || uptime > 100) {
        this.showError('Masukkan nilai antara 0 dan 100');
        return false;
      }
      return true;
    }

    showError(message: string): void {
      if (this.errorMessage) {
        this.errorMessage.textContent = message;
        this.errorMessage.classList.remove('hidden');
      }
    }

    clearError(): void {
      if (this.errorMessage) {
        this.errorMessage.classList.add('hidden');
      }
    }

    calculateDowntime(): void {
      const uptimeValue = parseFloat(this.input?.value || '0');

      if (!this.validateInput(uptimeValue)) {
        this.hideResults();
        return;
      }

      this.clearError();

      // Calculate downtime percentage
      const downtimePercentage = 100 - uptimeValue;

      // Time periods in minutes
      const minutesPerYear = 365.25 * 24 * 60; // Account for leap years
      const minutesPerMonth = 30.44 * 24 * 60; // Average month
      const minutesPerWeek = 7 * 24 * 60;
      const minutesPerDay = 24 * 60;

      // Calculate downtime in minutes for each period
      const yearlyDowntimeMinutes = (downtimePercentage / 100) * minutesPerYear;
      const monthlyDowntimeMinutes = (downtimePercentage / 100) * minutesPerMonth;
      const weeklyDowntimeMinutes = (downtimePercentage / 100) * minutesPerWeek;
      const dailyDowntimeMinutes = (downtimePercentage / 100) * minutesPerDay;

      // Format and display results
      this.displayResult('yearlyDowntime', this.formatTime(yearlyDowntimeMinutes));
      this.displayResult('monthlyDowntime', this.formatTime(monthlyDowntimeMinutes));
      this.displayResult('weeklyDowntime', this.formatTime(weeklyDowntimeMinutes));
      this.displayResult('dailyDowntime', this.formatTime(dailyDowntimeMinutes));

      this.showResults();
    }

    formatTime(minutes: number): string {
      if (minutes < 1) {
        const seconds = Math.round(minutes * 60);
        return `${seconds} detik`;
      } else if (minutes < 60) {
        return `${minutes.toFixed(1)} menit`;
      } else if (minutes < 1440) { // Less than a day
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = Math.round(minutes % 60);
        if (remainingMinutes === 0) {
          return `${hours} jam`;
        }
        return `${hours} jam ${remainingMinutes} menit`;
      } else {
        const days = Math.floor(minutes / 1440);
        const remainingHours = Math.round((minutes % 1440) / 60);
        if (remainingHours === 0) {
          return `${days} hari`;
        }
        return `${days} hari ${remainingHours} jam`;
      }
    }

    displayResult(elementId: string, value: string): void {
      const element = document.getElementById(elementId);
      if (element) {
        element.textContent = value;
      }
    }

    showResults(): void {
      if (this.resultsContainer) {
        this.resultsContainer.classList.remove('hidden');
        this.resultsContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }

    hideResults(): void {
      if (this.resultsContainer) {
        this.resultsContainer.classList.add('hidden');
      }
    }
  }

  // Initialize calculator when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new UptimeCalculator();
  });
</script>