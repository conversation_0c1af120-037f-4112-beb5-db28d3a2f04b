---
import Layout from '~/layouts/PageLayout.astro';
import { Icon } from 'astro-icon/components';

const metadata = {
  title: 'Kalkulator Uptime - Hitung Downtime dari Persentase Uptime | Penasihat Hosting',
  description: 'Kalkulator uptime untuk menghitung waktu downtime berdasarkan persentase uptime. Ketahui berapa lama website Anda akan offline dalam setahun, sebulan, seminggu, atau sehari.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/kalkulator-uptime.webp',
      }
    ]
  }
};
---

<Layout metadata={metadata}>
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white py-16 md:py-24">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <div class="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-sm rounded-2xl mb-8">
          <Icon name="tabler:clock" class="w-10 h-10 text-white" />
        </div>
        <h1 class="text-4xl md:text-6xl font-bold mb-6 tracking-tight">
          Kalkulator Uptime
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto mb-12 leading-relaxed">
          Hitung waktu downtime berdasarkan persentase uptime hosting Anda. Ketahui berapa lama website akan offline dalam periode waktu tertentu.
        </p>

        <!-- Quick Preset Buttons -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
          <button class="preset-btn-hero" data-value="99">99%</button>
          <button class="preset-btn-hero" data-value="99.9">99.9%</button>
          <button class="preset-btn-hero" data-value="99.99">99.99%</button>
          <button class="preset-btn-hero" data-value="99.999">99.999%</button>
        </div>

        <div class="text-blue-200 text-sm">
          Pilih preset atau masukkan nilai kustom di bawah
        </div>
      </div>
    </div>
  </section>

  <!-- Calculator Section -->
  <section class="py-16 md:py-24 bg-gray-50 dark:bg-slate-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Calculator Form -->
      <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-xl border border-gray-200 dark:border-slate-700 overflow-hidden">
        <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-8 py-6">
          <h2 class="text-2xl font-bold text-white flex items-center">
            <Icon name="tabler:calculator" class="w-7 h-7 mr-3" />
            Kalkulator Uptime
          </h2>
          <p class="text-blue-100 mt-2">Masukkan persentase uptime untuk melihat estimasi downtime</p>
        </div>

        <div class="p-8">
          <form id="uptimeForm" class="space-y-6">
            <div>
              <label for="uptimeInput" class="block text-lg font-semibold text-[var(--aw-color-text-heading)] mb-3">
                Persentase Uptime
              </label>
              <div class="relative">
                <input
                  type="number"
                  id="uptimeInput"
                  name="uptime"
                  min="0"
                  max="100"
                  step="0.001"
                  placeholder="99.9"
                  class="w-full px-6 py-4 text-2xl font-bold border-2 border-gray-300 dark:border-slate-600 rounded-xl bg-white dark:bg-slate-700 text-[var(--aw-color-text-default)] focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200"
                />
                <div class="absolute inset-y-0 right-0 flex items-center pr-6">
                  <span class="text-[var(--aw-color-text-muted)] text-2xl font-bold">%</span>
                </div>
              </div>
              <div id="errorMessage" class="mt-3 text-sm text-red-600 dark:text-red-400 hidden font-medium"></div>
            </div>

            <!-- Quick Preset Buttons -->
            <div class="space-y-3">
              <p class="text-sm font-medium text-[var(--aw-color-text-muted)]">Atau pilih preset umum:</p>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                <button type="button" class="preset-btn" data-value="99">99%</button>
                <button type="button" class="preset-btn" data-value="99.9">99.9%</button>
                <button type="button" class="preset-btn" data-value="99.99">99.99%</button>
                <button type="button" class="preset-btn" data-value="99.999">99.999%</button>
              </div>
            </div>

            <button
              type="submit"
              class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 px-8 rounded-xl transition-all duration-200 flex items-center justify-center text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Icon name="tabler:calculator" class="w-6 h-6 mr-3" />
              Hitung Downtime
            </button>
          </form>
        </div>
      </div>

      <!-- Results Display -->
      <div id="resultsContainer" class="hidden mt-8">
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-xl border border-gray-200 dark:border-slate-700 overflow-hidden">
          <div class="bg-gradient-to-r from-red-600 to-red-700 px-8 py-6">
            <h3 class="text-2xl font-bold text-white flex items-center">
              <Icon name="tabler:clock-x" class="w-7 h-7 mr-3" />
              Estimasi Waktu Downtime
            </h3>
            <p class="text-red-100 mt-2">Berdasarkan persentase uptime yang Anda masukkan</p>
          </div>

          <div class="p-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <!-- Per Year -->
              <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-2 border-red-200 dark:border-red-800 rounded-xl p-6 text-center">
                <Icon name="tabler:calendar-time" class="w-12 h-12 text-red-600 dark:text-red-400 mx-auto mb-4" />
                <h4 class="font-bold text-red-800 dark:text-red-200 mb-2 text-lg">Per Tahun</h4>
                <p id="yearlyDowntime" class="text-2xl font-bold text-red-600 dark:text-red-400">-</p>
              </div>

              <!-- Per Month -->
              <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-2 border-orange-200 dark:border-orange-800 rounded-xl p-6 text-center">
                <Icon name="tabler:calendar-event" class="w-12 h-12 text-orange-600 dark:text-orange-400 mx-auto mb-4" />
                <h4 class="font-bold text-orange-800 dark:text-orange-200 mb-2 text-lg">Per Bulan</h4>
                <p id="monthlyDowntime" class="text-2xl font-bold text-orange-600 dark:text-orange-400">-</p>
              </div>

              <!-- Per Week -->
              <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-2 border-yellow-200 dark:border-yellow-800 rounded-xl p-6 text-center">
                <Icon name="tabler:calendar-stats" class="w-12 h-12 text-yellow-600 dark:text-yellow-400 mx-auto mb-4" />
                <h4 class="font-bold text-yellow-800 dark:text-yellow-200 mb-2 text-lg">Per Minggu</h4>
                <p id="weeklyDowntime" class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">-</p>
              </div>

              <!-- Per Day -->
              <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-2 border-blue-200 dark:border-blue-800 rounded-xl p-6 text-center">
                <Icon name="tabler:calendar" class="w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                <h4 class="font-bold text-blue-800 dark:text-blue-200 mb-2 text-lg">Per Hari</h4>
                <p id="dailyDowntime" class="text-2xl font-bold text-blue-600 dark:text-blue-400">-</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Placeholder Content (shown before calculation) -->
      <div id="placeholderContent" class="mt-8">
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-xl border border-gray-200 dark:border-slate-700 overflow-hidden">
          <div class="bg-gradient-to-r from-gray-600 to-gray-700 px-8 py-6">
            <h3 class="text-2xl font-bold text-white flex items-center">
              <Icon name="tabler:info-circle" class="w-7 h-7 mr-3" />
              Contoh Perhitungan
            </h3>
            <p class="text-gray-100 mt-2">Lihat estimasi downtime untuk berbagai tingkat uptime</p>
          </div>

          <div class="p-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <!-- Example: 99% -->
              <div class="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-2 border-red-200 dark:border-red-800 rounded-xl p-6 text-center">
                <Icon name="tabler:calendar-time" class="w-12 h-12 text-red-600 dark:text-red-400 mx-auto mb-4" />
                <h4 class="font-bold text-red-800 dark:text-red-200 mb-2 text-lg">99% Uptime</h4>
                <p class="text-xl font-bold text-red-600 dark:text-red-400">3.65 hari/tahun</p>
                <p class="text-sm text-red-500 dark:text-red-400 mt-1">7.31 jam/bulan</p>
              </div>

              <!-- Example: 99.9% -->
              <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-2 border-orange-200 dark:border-orange-800 rounded-xl p-6 text-center">
                <Icon name="tabler:calendar-event" class="w-12 h-12 text-orange-600 dark:text-orange-400 mx-auto mb-4" />
                <h4 class="font-bold text-orange-800 dark:text-orange-200 mb-2 text-lg">99.9% Uptime</h4>
                <p class="text-xl font-bold text-orange-600 dark:text-orange-400">8.77 jam/tahun</p>
                <p class="text-sm text-orange-500 dark:text-orange-400 mt-1">43.8 menit/bulan</p>
              </div>

              <!-- Example: 99.99% -->
              <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-2 border-yellow-200 dark:border-yellow-800 rounded-xl p-6 text-center">
                <Icon name="tabler:calendar-stats" class="w-12 h-12 text-yellow-600 dark:text-yellow-400 mx-auto mb-4" />
                <h4 class="font-bold text-yellow-800 dark:text-yellow-200 mb-2 text-lg">99.99% Uptime</h4>
                <p class="text-xl font-bold text-yellow-600 dark:text-yellow-400">52.6 menit/tahun</p>
                <p class="text-sm text-yellow-500 dark:text-yellow-400 mt-1">4.38 menit/bulan</p>
              </div>

              <!-- Example: 99.999% -->
              <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-2 border-green-200 dark:border-green-800 rounded-xl p-6 text-center">
                <Icon name="tabler:calendar" class="w-12 h-12 text-green-600 dark:text-green-400 mx-auto mb-4" />
                <h4 class="font-bold text-green-800 dark:text-green-200 mb-2 text-lg">99.999% Uptime</h4>
                <p class="text-xl font-bold text-green-600 dark:text-green-400">5.26 menit/tahun</p>
                <p class="text-sm text-green-500 dark:text-green-400 mt-1">26.3 detik/bulan</p>
              </div>
            </div>

            <div class="mt-8 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
              <div class="flex items-start">
                <Icon name="tabler:lightbulb" class="w-6 h-6 text-blue-600 dark:text-blue-400 mt-1 mr-4 flex-shrink-0" />
                <div>
                  <h4 class="font-bold text-blue-800 dark:text-blue-200 mb-2">Tips Memilih Uptime</h4>
                  <ul class="text-blue-700 dark:text-blue-300 space-y-1 text-sm">
                    <li>• <strong>99%</strong> - Cocok untuk website personal atau blog</li>
                    <li>• <strong>99.9%</strong> - Standar untuk website bisnis kecil</li>
                    <li>• <strong>99.99%</strong> - Diperlukan untuk e-commerce dan aplikasi bisnis</li>
                    <li>• <strong>99.999%</strong> - Untuk aplikasi mission-critical dan enterprise</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Information Section -->
  <section class="py-12 md:py-16 bg-gray-50 dark:bg-slate-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-[var(--aw-color-text-heading)] text-center mb-12">
        Memahami Uptime dan Downtime
      </h2>

      <div class="grid md:grid-cols-2 gap-8 mb-12">
        <!-- What is Uptime -->
        <div class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700">
          <div class="flex items-center mb-4">
            <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full mr-4">
              <Icon name="tabler:server" class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <h3 class="text-xl font-bold text-[var(--aw-color-text-heading)]">Apa itu Uptime?</h3>
          </div>
          <p class="text-[var(--aw-color-text-default)] leading-relaxed">
            Uptime adalah persentase waktu dimana server atau website Anda dapat diakses dan berfungsi normal.
            Uptime 99.9% berarti website Anda online 99.9% dari total waktu dalam periode tertentu.
          </p>
        </div>

        <!-- What is Downtime -->
        <div class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-slate-700">
          <div class="flex items-center mb-4">
            <div class="bg-red-100 dark:bg-red-900/30 p-3 rounded-full mr-4">
              <Icon name="tabler:server-off" class="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <h3 class="text-xl font-bold text-[var(--aw-color-text-heading)]">Apa itu Downtime?</h3>
          </div>
          <p class="text-[var(--aw-color-text-default)] leading-relaxed">
            Downtime adalah waktu dimana website atau server tidak dapat diakses oleh pengunjung.
            Semakin tinggi uptime, semakin sedikit downtime yang dialami website Anda.
          </p>
        </div>
      </div>

      <!-- Why It Matters -->
      <div class="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-slate-700">
        <div class="flex items-center mb-6">
          <div class="bg-primary/10 dark:bg-primary/20 p-3 rounded-full mr-4">
            <Icon name="tabler:trending-up" class="w-6 h-6 text-primary" />
          </div>
          <h3 class="text-2xl font-bold text-[var(--aw-color-text-heading)]">Mengapa Uptime Penting?</h3>
        </div>

        <div class="grid md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Icon name="tabler:users" class="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h4 class="font-semibold text-[var(--aw-color-text-heading)] mb-2">Pengalaman Pengguna</h4>
            <p class="text-sm text-[var(--aw-color-text-muted)]">Website yang sering down akan membuat pengunjung frustrasi dan beralih ke kompetitor</p>
          </div>

          <div class="text-center">
            <div class="bg-green-100 dark:bg-green-900/30 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Icon name="tabler:currency-dollar" class="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h4 class="font-semibold text-[var(--aw-color-text-heading)] mb-2">Kerugian Finansial</h4>
            <p class="text-sm text-[var(--aw-color-text-muted)]">Downtime dapat menyebabkan kehilangan penjualan dan revenue, terutama untuk e-commerce</p>
          </div>

          <div class="text-center">
            <div class="bg-purple-100 dark:bg-purple-900/30 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Icon name="tabler:search" class="w-8 h-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h4 class="font-semibold text-[var(--aw-color-text-heading)] mb-2">SEO Impact</h4>
            <p class="text-sm text-[var(--aw-color-text-muted)]">Search engine dapat menurunkan ranking website yang sering mengalami downtime</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Reference Table Section -->
  <section class="py-12 md:py-16">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-[var(--aw-color-text-heading)] text-center mb-12">
        Tabel Referensi Uptime
      </h2>

      <div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-gray-200 dark:border-slate-700 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50 dark:bg-slate-700">
              <tr>
                <th class="px-6 py-4 text-left text-xs font-medium text-[var(--aw-color-text-muted)] uppercase tracking-wider">Uptime</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-[var(--aw-color-text-muted)] uppercase tracking-wider">Downtime/Tahun</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-[var(--aw-color-text-muted)] uppercase tracking-wider">Downtime/Bulan</th>
                <th class="px-6 py-4 text-left text-xs font-medium text-[var(--aw-color-text-muted)] uppercase tracking-wider">Kategori</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-slate-600">
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">90%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 dark:text-red-400">36.53 hari</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 dark:text-red-400">73 jam</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Poor</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">95%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-orange-600 dark:text-orange-400">18.26 hari</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-orange-600 dark:text-orange-400">36.5 jam</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Fair</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">99%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600 dark:text-yellow-400">3.65 hari</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-yellow-600 dark:text-yellow-400">7.31 jam</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Good</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">99.9%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400">8.77 jam</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400">43.8 menit</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Very Good</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">99.99%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">52.6 menit</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">4.38 menit</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Excellent</td>
              </tr>
              <tr class="hover:bg-gray-50 dark:hover:bg-slate-700/50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--aw-color-text-default)]">99.999%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">5.26 menit</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">26.3 detik</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-[var(--aw-color-text-default)]">Excellent</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </section>
</Layout>

<style>
  /* NYC Design System Inspired Styles */
  .preset-btn {
    @apply px-4 py-3 text-sm font-bold border-2 border-blue-300 text-blue-700 bg-blue-50 rounded-xl hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg;
  }

  .preset-btn.active {
    @apply bg-blue-600 text-white border-blue-600 shadow-lg;
  }

  .preset-btn-hero {
    @apply px-6 py-3 text-lg font-bold bg-white/10 backdrop-blur-sm text-white border-2 border-white/20 rounded-xl hover:bg-white hover:text-blue-700 transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-xl;
  }

  .preset-btn-hero.active {
    @apply bg-white text-blue-700 shadow-xl;
  }

  /* NYC-style focus states */
  input:focus {
    outline: none;
  }

  /* NYC-style animations */
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-in {
    animation: slideInUp 0.5s ease-out;
  }

  /* NYC-style card hover effects */
  .nyc-card {
    @apply transition-all duration-300 hover:shadow-2xl hover:-translate-y-1;
  }

  /* NYC-style gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #1e40af, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
</style>

<script>
  // Uptime Calculator Logic
  class UptimeCalculator {
    // Explicit property declarations with proper types
    form: HTMLFormElement | null;
    input: HTMLInputElement | null;
    errorMessage: HTMLElement | null;
    resultsContainer: HTMLElement | null;
    placeholderContent: HTMLElement | null;
    presetButtons: NodeListOf<Element>;
    heroPresetButtons: NodeListOf<Element>;

    constructor() {
      this.form = document.getElementById('uptimeForm') as HTMLFormElement | null;
      this.input = document.getElementById('uptimeInput') as HTMLInputElement | null;
      this.errorMessage = document.getElementById('errorMessage') as HTMLElement | null;
      this.resultsContainer = document.getElementById('resultsContainer') as HTMLElement | null;
      this.placeholderContent = document.getElementById('placeholderContent') as HTMLElement | null;
      this.presetButtons = document.querySelectorAll('.preset-btn');
      this.heroPresetButtons = document.querySelectorAll('.preset-btn-hero');

      // Check if all required elements exist
      if (!this.form || !this.input || !this.errorMessage || !this.resultsContainer || !this.placeholderContent) {
        console.error('Required elements not found');
        return;
      }

      this.initEventListeners();
    }

    initEventListeners(): void {
      // Form submission
      this.form?.addEventListener('submit', (e: Event) => {
        e.preventDefault();
        this.calculateDowntime();
      });

      // Real-time calculation on input
      this.input?.addEventListener('input', () => {
        this.clearError();
        if (this.input?.value) {
          this.calculateDowntime();
        } else {
          this.hideResults();
        }
      });

      // Preset buttons in calculator section
      this.presetButtons?.forEach((btn: Element) => {
        btn.addEventListener('click', () => {
          const value = (btn as HTMLElement).dataset.value;
          if (this.input && value) {
            this.input.value = value;
            this.updateActivePreset(btn as HTMLElement, 'preset-btn');
            this.calculateDowntime();
            this.scrollToCalculator();
          }
        });
      });

      // Hero preset buttons
      this.heroPresetButtons?.forEach((btn: Element) => {
        btn.addEventListener('click', () => {
          const value = (btn as HTMLElement).dataset.value;
          if (this.input && value) {
            this.input.value = value;
            this.updateActivePreset(btn as HTMLElement, 'preset-btn-hero');
            this.calculateDowntime();
            this.scrollToCalculator();
          }
        });
      });
    }

    updateActivePreset(activeBtn: HTMLElement, buttonClass: string): void {
      // Clear active state from all buttons of the same type
      if (buttonClass === 'preset-btn') {
        this.presetButtons?.forEach((btn: Element) => btn.classList.remove('active'));
      } else if (buttonClass === 'preset-btn-hero') {
        this.heroPresetButtons?.forEach((btn: Element) => btn.classList.remove('active'));
      }
      activeBtn?.classList.add('active');
    }

    scrollToCalculator(): void {
      const calculatorSection = document.querySelector('#uptimeForm');
      if (calculatorSection) {
        calculatorSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }

    validateInput(uptime: number): boolean {
      if (isNaN(uptime) || uptime < 0 || uptime > 100) {
        this.showError('Masukkan nilai antara 0 dan 100');
        return false;
      }
      return true;
    }

    showError(message: string): void {
      if (this.errorMessage) {
        this.errorMessage.textContent = message;
        this.errorMessage.classList.remove('hidden');
      }
    }

    clearError(): void {
      if (this.errorMessage) {
        this.errorMessage.classList.add('hidden');
      }
    }

    calculateDowntime(): void {
      const uptimeValue = parseFloat(this.input?.value || '0');

      if (!this.validateInput(uptimeValue)) {
        this.hideResults();
        return;
      }

      this.clearError();

      // Calculate downtime percentage
      const downtimePercentage = 100 - uptimeValue;

      // Time periods in minutes
      const minutesPerYear = 365.25 * 24 * 60; // Account for leap years
      const minutesPerMonth = 30.44 * 24 * 60; // Average month
      const minutesPerWeek = 7 * 24 * 60;
      const minutesPerDay = 24 * 60;

      // Calculate downtime in minutes for each period
      const yearlyDowntimeMinutes = (downtimePercentage / 100) * minutesPerYear;
      const monthlyDowntimeMinutes = (downtimePercentage / 100) * minutesPerMonth;
      const weeklyDowntimeMinutes = (downtimePercentage / 100) * minutesPerWeek;
      const dailyDowntimeMinutes = (downtimePercentage / 100) * minutesPerDay;

      // Format and display results
      this.displayResult('yearlyDowntime', this.formatTime(yearlyDowntimeMinutes));
      this.displayResult('monthlyDowntime', this.formatTime(monthlyDowntimeMinutes));
      this.displayResult('weeklyDowntime', this.formatTime(weeklyDowntimeMinutes));
      this.displayResult('dailyDowntime', this.formatTime(dailyDowntimeMinutes));

      this.showResults();
    }

    formatTime(minutes: number): string {
      if (minutes < 1) {
        const seconds = Math.round(minutes * 60);
        return `${seconds} detik`;
      } else if (minutes < 60) {
        return `${minutes.toFixed(1)} menit`;
      } else if (minutes < 1440) { // Less than a day
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = Math.round(minutes % 60);
        if (remainingMinutes === 0) {
          return `${hours} jam`;
        }
        return `${hours} jam ${remainingMinutes} menit`;
      } else {
        const days = Math.floor(minutes / 1440);
        const remainingHours = Math.round((minutes % 1440) / 60);
        if (remainingHours === 0) {
          return `${days} hari`;
        }
        return `${days} hari ${remainingHours} jam`;
      }
    }

    displayResult(elementId: string, value: string): void {
      const element = document.getElementById(elementId);
      if (element) {
        element.textContent = value;
      }
    }

    showResults(): void {
      if (this.resultsContainer && this.placeholderContent) {
        // Hide placeholder and show results with animation
        this.placeholderContent.classList.add('hidden');
        this.resultsContainer.classList.remove('hidden');
        this.resultsContainer.classList.add('animate-slide-in');

        // Smooth scroll to results
        setTimeout(() => {
          this.resultsContainer?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, 100);
      }
    }

    hideResults(): void {
      if (this.resultsContainer && this.placeholderContent) {
        // Hide results and show placeholder
        this.resultsContainer.classList.add('hidden');
        this.resultsContainer.classList.remove('animate-slide-in');
        this.placeholderContent.classList.remove('hidden');
      }
    }
  }

  // Initialize calculator when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new UptimeCalculator();
  });
</script>