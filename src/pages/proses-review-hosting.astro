---
import Layout from '~/layouts/PageLayout.astro';
import CustomStyles from '~/components/CustomStyles.astro';

const metadata = {
  title: 'Proses Review Hosting - Penasihat Hosting',
  description: 'Temukan bagaimana kami melakukan review hosting secara menyeluruh dan objektif di Penasihat Hosting. Pelajari metodologi pengujian kami yang ketat.',
  openGraph: {
    images: [
      {
        url: 'https://img.penasihathosting.com/2025/May/default-og-image.webp', // Using a default image URL
      }
    ]
  }
};
---

<Layout metadata={metadata}>
  <CustomStyles />

  <!-- Hero Section -->
  <section class="py-8">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 text-center">
      <h1 class="text-5xl font-bold leading-tight text-black dark:text-white tracking-tight">
        Proses Review Hosting
      </h1>
    </div>
  </section>

  <!-- Content Section -->
  <section class="py-8">
    <div class="max-w-3xl mx-auto px-5 sm:px-6">
      <article class="prose dark:prose-invert max-w-none text-[var(--aw-color-text-default)]">
        <div class="mb-8">
          <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-4">Langkah 1</h2>
          <h3 class="text-xl font-semibold text-[var(--aw-color-text-heading)] mb-3">Mencari Penyedia Hosting Dengan Reputasi Yang Baik</h3>
          <p>
            <a href="https://penasihathosting.com/direktori/kategori/web-hosting/">Ada ratusan penyedia hosting</a> yang menawarkan berbagai macam fitur, harga dan kualitas yang tentu saja berbeda.
          </p>
          <p class="mt-4">
            Di Penasihat Hosting, kami memiliki kriteria-kriteria khusus ketika memutuskan penyedia hosting mana yang akan di review.
          </p>
          <p class="mt-4">
            Poin pertama yang menjadi tolak ukur kami adalah reputasi perusahaan. Reputasi (umur penyedia hosting harus lebih dari 3 tahun). Kemudian, memiliki banyak review atau testimonial positif, karena penyedia hosting yang belum memiliki reputasi kami jamin lebih beresiko. Anda tidak ingin memilih penyedia hosting yang beresiko, bukan?
          </p>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-4">Langkah 2</h2>
          <h3 class="text-xl font-semibold text-[var(--aw-color-text-heading)] mb-3">Membeli Semua Paket Web Hosting yang Di Review</h3>
          <p>
            Tidak seperti sebagian besar website review hosting lainnya, kami benar-benar mengeluarkan uang dari dompet kami sendiri untuk membeli semua web hosting kami review.
          </p>
          <p class="mt-4">
            Artinya, kami adalah pengguna/user. Tidak ada review yang lebih baik daripada review dari pengguna ke pengguna bukan?
          </p>
          <p class="mt-4">
            Kesulitan yang kami hadapi dalam tahap ini adalah menentukan paket hosting yang akan dibeli, karena <a href="https://penasihathosting.com/">penyedia hosting di Indonesia</a> menawarkan berbagai jenis paket hosting yang jumlahnya sangat banyak (bisa lebih dari 5+ jenis paket shared hosting).
          </p>
          <p class="mt-4">
            Dalam hal ini, kami menentukan paket hosting yang menurut kami adil dan dapat dibandingkan satu sama lain.
          </p>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-4">Langkah 3</h2>
          <h3 class="text-xl font-semibold text-[var(--aw-color-text-heading)] mb-3">Mengenal Semua Hal Tentang Penyedia Hosting yang Kami Gunakan</h3>
          <p>
            Kami melihat apa yang mereka tawarkan, seperti apa saja paket hosting yang ditawarkan, bagaimana harganya dibanding dengan penyedia hosting lainnya.Lalu, kami juga melihat ketentuan kontrak/perjanjian untuk mencari tahu apakah ada biaya tersembunyi atau hal yang tidak adil dimana pelanggan harus mengetahuinya.
          </p>
          <p class="mt-4">
            Secara keseluruhan, kami memperhatikan:
          </p>
          <ul class="list-disc pl-6 space-y-1 mt-2">
            <li>Paket hosting yang ditawarkan</li>
            <li>Harga hosting & mungkin adanya biaya tersembunyi</li>
            <li>Bahasa atau klaim yang digunakan (seperti 100% uptime, unlimited storage dan bandwidth. Percayalah ketiganya itu mustahil)</li>
            <li>Garansi yang ditawarkan</li>
            <li>Ketersediaan layanan suport (live chat, tiket, phone - apakah tersedia 24/7)</li>
          </ul>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-4">Langkah 4</h2>
          <h3 class="text-xl font-semibold text-[var(--aw-color-text-heading)] mb-3">Mempersiapkan Website Test dan Mulai Memonitoring Uptime dan Melakukan Pengujian Load Testing</h3>
          <p>
            Untuk melihat bagaimana kualitas / performance semua web host yang kami review, kami mulai mempersiapkan website test (menggunakan WordPress).
          </p>
          <p class="mt-4">
            Kami menggunakan tool premium Uptimia.com untuk memonitoring uptime dan Locust.io untuk mengukur kecepatan response server.
          </p>
          <p class="mt-4 font-semibold">
            Data tidak pernah berbohong.
          </p>
          <p class="mt-4">
            Jadi, secara langsung kami melacak:
          </p>
          <ul class="list-disc pl-6 space-y-1 mt-2">
            <li>Seberapa sering server naik atau turun (persentase uptime)</li>
            <li>Bagaimana response server ketika meng-handle pengunjung yang ramai (load testing)</li>
          </ul>

          <div class="mt-6 bg-[var(--aw-color-bg-page)] dark:bg-[var(--aw-color-bg-page-dark)] border border-[var(--aw-color-text-muted)/20] p-6 rounded-lg">
            <h4 class="text-lg font-semibold text-[var(--aw-color-text-heading)] mb-3">Tentang Monitoring Uptime</h4>
            <p>
              Kami menggunakan Pingdom.com untuk memonitoring uptime semua provider hosting yang kami review.
            </p>
            <p class="mt-4">
              Monitoring uptime di lakukan setiap 1 menit sekali (interval 1 menit) dan di monitoring dari beberapa server di Asia Pacific.
            </p>
            <p class="mt-4">
              Mungkin Anda bertanya-tanya, bagaimana sebuah server bisa dikatakan down? Jawaban sederhananya adalah ketika Pingdom mendeteksi bahwa sebuah website tidak bisa diakses atau error yang di konfirmasi dari 2 lokasi server.
            </p>
            <p class="mt-4">
              Itu artinya, secara teknis, tidak mungkin terjadi false positives atau false alerts karena adanya konfirmasi test dari lokasi server yang kedua.
            </p>
            <p class="mt-4">
              Dan mengapa kami tidak menggunakan server yang berlokasi di Indonesia?
            </p>
            <p class="mt-4">
              Sudah pernah.
            </p>
            <p class="mt-4">
              Kami sudah pernah menggunakan server di Jakarta untuk memonitorig uptime dan alat yang kami gunakan adalah Site24x7. Tetapi, ada begitu banyak masalah sehingga membuat kami tidak percaya lagi akan keakuratan data dari server di Jakarta yang digunakan oleh Site24x7.
            </p>
            <p class="mt-4">
              Akhirnya, mulai Agustus 2019, kami beralih ke Pingdom dan menemukan bahwa data yang kami peroleh jauh lebih akurat dan lebih bisa dibuktikan.
            </p>
            <p class="text-base text-gray-800 dark:text-gray-300 mt-2">
              Untuk itu, kami memohon maaf yang sebesar-besarnya jika data sebelumnya yang kami sajikan dengan Site24x7 tidak akurat.
            </p>
            <p class="text-base text-gray-800 dark:text-gray-300 mt-2">
              Untungnya, kami sudah meng-update semuanya dengan Pingdom.com.
            </p>
          </div>

          <div class="mt-6 bg-[var(--aw-color-bg-page)] dark:bg-[var(--aw-color-bg-page-dark)] border border-[var(--aw-color-text-muted)/20] p-6 rounded-lg">
            <h4 class="text-lg font-semibold text-[var(--aw-color-text-heading)] mb-3">Tentang Monitoring Speed (TTFB)</h4>
            <p>
              Untuk menguji seberapa cepat kecepatan server dari semua provider hosting yang kami review, kami melakukan pengujian menggunakan metrik TTFB atau time to first byte.
            </p>
            <p class="mt-4">
              TTFB adalah ukuran dari kecepatan server hosting dan memiliki kaitan yang erat dengan masalah dari sisi server. Artinya, kecepatan server hosting itu sendiri bisa dinilai dari TTFB. Cepat atau lambatnya bisa diukur menggunakan TTFB.
            </p>
            <p class="mt-4">
              Dalam melakukan pengujian, kami tidak mengaplikasikan caching apalagi CDN sehingga murni untuk mengukur kecepatan server itu sendiri.
            </p>
            <p class="mt-4">
              Pengujian kecepatan server atau TTFB ini akan dilakukan setiap update penelitian berlangsung.
            </p>
          </div>

          <div class="mt-6 bg-[var(--aw-color-bg-page)] dark:bg-[var(--aw-color-bg-page-dark)] border border-[var(--aw-color-text-muted)/20] p-6 rounded-lg">
            <h4 class="text-lg font-semibold text-[var(--aw-color-text-heading)] mb-3">Pengujian Load Impact</h4>
            <p>
              Kekurangan dari pengujian kecepatan atau TTFB yang kami lakukan adalah tidak dapat mengukur seberapa baik response server ketika menerima pengunjung yang ramai datang secara bersamaan.
            </p>
            <p class="mt-4">
              Jadi, kami melakukan pengujian tambahan, yaitu pengujian Load impact.
            </p>
            <blockquote class="border-l-4 border-[var(--aw-color-primary)] pl-4 italic my-4 text-[var(--aw-color-text-muted)]">
              Sederhananya, Anda ingin kecepatan website Anda sama cepatnya baik ketika pengunjung pertama datang maupun ketika pengunjung ke-25 datang atau ke 50 atau ke 2000 datang, bukan?
            </blockquote>
            <p>
              Oleh karena itu, dengan adanya test Load Impact, Anda akan bisa melihat bagaimana server web hosting dapat meng-handle semua permintaan yang masuk.
            </p>
            <p class="mt-4">
              Untuk shared hosting, kami melakukan pengujian dengan mengirimkan 10 virtual user (pengunjung), sedangkan untuk managed cloud VPS adalah 200 virtual user.
            </p>
          </div>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-4">Langkah 5</h2>
          <h3 class="text-xl font-semibold text-[var(--aw-color-text-heading)] mb-3">Menguji Layanan Support (seperti Keramahan, Ketersediaan 24/7, dan Pengetahuan Teknisnya)</h3>
          <p>
            Tidak semua pelanggan mengerti apa itu uptime dan tidak pula mengetahui seberapa cepat server hosting yang mereka gunakan, tapi ketika mereka mendapatkan pelayanan yang bagus, kami yakin mereka akan mengatakan hosting yang mereka gunakan bagus.
          </p>
          <p class="mt-4">
            Pelayanan yang buruk dapat merusak hosting yang sebenarnya bagus (dari sisi performances).
          </p>
          <p class="mt-4">
            Kami menguji support dengan melakukan pengujian teknis. Secara singkat, kami sengaja membuat error pada website test kami, kemudian meminta staf support dari semua provider Indonesia yang kami review untuk memperbaiki error yang terjadi.
          </p>
          <p class="mt-4">
            Kami mengevaluasi hal-hal berikut:
          </p>
          <ul class="list-disc pl-6 space-y-1 mt-2">
            <li>Kecepatan response</li>
            <li>Komunikasi (apakah ramah dan mudah dipahami?)</li>
            <li>Pengetahuan tentang hal-hal teknis</li>
            <li>Keakuratan dalam memberikan saran/nasihat/arahan</li>
            <li>Ketersediaan support selama 24/7</li>
          </ul>
        </div>

        <div class="mb-8">
          <h2 class="text-2xl font-bold text-[var(--aw-color-text-heading)] mb-4">Langkah 6</h2>
          <h3 class="text-xl font-semibold text-[var(--aw-color-text-heading)] mb-3">Mengumpulkan Semua Data, Menulis Review dan Merangkum Hasil Penelitian</h3>
          <p>
            Kami mengumpulkan semua data berdasarkan semua hal yang telah kami pelajari, mulai dari fitur, harga, performances test (uptime dan load testing), support dan masa garansi sebelum menulis review hosting.
          </p>
          <p class="mt-4">
            Kemudian kami publikasikan dan update hasil penelitian secara rutin di halaman depan website PenasihatHosting.com
          </p>
          <p class="mt-6 font-semibold">
            Kami selalu menerima masukan dan kritik. Anda dapat menghubungi kami melalui email di <a href="mailto:<EMAIL>"><EMAIL></a>.
          </p>
          <p class="mt-4 text-[var(--aw-color-text-muted)] italic">
            Halaman ini terakhir di update: 26 September 2024
          </p>
        </div>
      </article>
    </div>
  </section>
</Layout>
