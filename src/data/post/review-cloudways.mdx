---
title: "Review Cloudways"
publishDate: 2020-01-08
updateDate: 2025-05-12
category: "Review Hosting"
image: https://img.penasihathosting.com/2025/May/review-cloudways.webp
tags:
  - "Cloud Panel"
  - "featured"
excerpt: "Review lengkap Cloudways, platform managed cloud hosting yang populer, membahas kelebihan dan kekurangannya serta perbandingannya dengan provider lain. Cocok untuk pemula hingga developer."
metadata:
  title: "Review Cloudways: Managed Cloud Hosting untuk Pemula & Developer"
  description: "Temukan kelebihan dan kekurangan Cloudways, platform managed cloud hosting populer. Bandingkan performa & harga dengan alternatif lain. Cocok untuk website bisnis online."
  featured: true
---

import PromoBanner from '~/components/ui/PromoBanner.astro';

**Disclosure:** Support Anda membantu website ini terus berjalan. Saya menerima komisi $65 dari pembelian hosting Anda melalui link affiliasi tanpa ada biaya tambahan dibebankan kepada Anda. Terima kasih atas support Anda. [Baca lebih lengkap…](/advertiser-disclosure/)

[Cloudways](https://penasihathosting.com/go/cloudways) telah berdiri sejak tahun 2011. Mereka berkantor pusat di Malta.

Jika Anda sudah melakukan _research_ sebelumnya terhadap provider managed [cloud hosting](https://penasihathosting.com/direktori/kategori/cloud-hosting/), Cloudways adalah salah satu nama yang sering disebut-sebut.

Selama 20+ tahun sejak berdirinya, mereka telah berhasil mengelola lebih dari 570.000 website dari total lebih dari 80k pelanggan dari seluruh dunia.

Sebuah pencapaian yang luar biasa, bukan?

Dan pada Agustus 2022 lalu, mereka telah diakuisisi atau telah menjadi bagian dari keluarga besar Digital Ocean, provider VPS yang saya yakin sebagian besar dari Anda pasti tahu.

<PromoBanner
  title="Cloudways Hosting"
  description="Dapatkan diskon 30% selama 2 bulan dengan kode promo di bawah ini. Gratis trial 3 hari tanpa perlu kartu kredit!"
  ctaText="Coba Cloudways Sekarang"
  ctaLink="https://penasihathosting.com/go/cloudways"
  promoCode="PHCW30"
  imageSrc="https://img.penasihathosting.com/2025/May/kode-promo-Cloudways.webp"
  imageAlt="Kode Promo Cloudways"
  disclaimer="Support Anda membantu website ini terus berjalan! Saya menggunakan link affiliasi, dimana saya akan menerima komisi $65 dari pembelian hosting Cloudways Anda. Anda tidak akan dikenakan biaya tambahan."
/>

**Mungkin Anda bertanya-tanya:**

## Mengapa perkembangan Cloudways boleh dibilang pesat?

Lebih lanjut, apa pendekatan yang mereka lakukan sehingga banyak pengguna beralih dari provider hosting tradisional ke Cloudways?

Secara singkat, Cloudways memberikan solusi bagi beberapa model pengguna, seperti:

- Pengguna yang tidak memiliki pengetahuan teknis untuk mengelola server sendiri

- Pengguna yang datang dari shared hosting, kemudian menginginkan [cloud VPS hosting](https://penasihathosting.com/direktori/kategori/unmanaged-vps/) untuk mendapatkan performa yang jauh lebih baik, tapi tidak ingin repot mengelola server sendiri

- Pengguna yang mencari alternatif dari [Managed WordPress Hosting](https://penasihathosting.com/direktori/kategori/wordpress-hosting/), seperti [Kinsta](https://penasihathosting.com/review-kinsta/) dan WP Engine, dengan harga yang lebih murah dan performance yang tidak kalah atau bahkan lebih baik

- Pengguna yang mencari alternatif dari Cloud Panel seperti [RunCloud](https://penasihathosting.com/review-runcloud/), GridPane, ServerPilot, dan lain-lain, karena tidak mau repot membuat dua buah account (account provider cloud hosting dan account cloud panel)

- Pengguna yang mencari alternatif dari RunCloud

**Pertanyaan pentingnya:**

- Apakah Cloudways adalah piilhan yang tepat untuk pemula? Bagaimana untuk developer?

- Apakah Cloudways telah dioptimalkan untuk website WordPress? Bagaimana untuk CMS lainnya, seperti Laravel, Drupal, Joomla, Magento, Moodle dan lainnya?

- Apakah performa nya cepat? Bagaimana jika dibandingkan dengan RunCloud?

- Apakah harganya masuk akal? _Worth it_ kah untuk pindah ke Cloudways?

Dalam review Cloudways ini, saya akan mencoba menjawab pertanyaan-pertanyaan tersebut, serta menggali apa yang menjadi keunikan mereka dan mengapa pendekatan yang mereka lakukan dapat memberikan lebih banyak fleksibilitas daripada provider lainnya.

## Kelebihan Menggunakan Cloudways

Sebelum menulis review Cloudways ini, saya telah mencoba Cloudways dan melakukan pengetesan speed, juga mencoba dashboard atau interface terbaru mereka sehingga membuat review ini up-to-date dan dapat membantu Anda dalam mengambil keputusan.

Tidak lupa juga saya menguji ulang kembali kualitas support nya setelah sebelumnya juga telah dilakukan pada tahun 2021.

Setelah meneliti semuanya, saya bisa menyimpulkan apa-apa saja yang menjadi kelebihan Cloudways berikut ini.

#### 1. Cloudways Adalah Sebuah Platform Untuk Mengelola Unmanaged Cloud VPS

Pertama dan yang perlu Anda ketahui, sebenarnya Cloudways bukanlah provider cloud hosting.

Mereka hanya menyediakan platform yang dapat Anda gunakan untuk mengelola cloud VPS hosting dari provider-provider tertentu yang telah mereka tetapkan.

Jadi, yang mereka tawarkan sebenarnya adalah pengelolaan server di dalam sebuah platform. Tetapi, berbeda dengan platform (mungkin lebih tepatnya cloud panel), seperti RunCloud, ServerPilot, GridPane, SpinupWP dan yang lainnya, dimana mereka mengharuskan Anda memiliki dua account:

- Satu account provider cloud hosting (katakanlah Digital Ocean),

- Satu account di cloud panel (katakanlah RunCloud)

Di Cloudways, karena mereka adalah sebuah platform, Anda hanya butuh sebuah account Cloudways saja. Inilah yang membedakan Cloudways dengan beberapa cloud panel yang saya sebutkan sebelumnya.

Ada lima provider cloud hosting yang dapat Anda pilih, diantaranya:

- **Digital Ocean:** Salah satu pioneer dalam teknologi cloud hosting dan merupakan pilihan paling populer dan termurah dengan biaya mulai dari $11/bulan atau $14/bulan untuk yang premium.

- **Vultr & Linode:** Sudah tidak tersedia sejak 2 May 2025

- **Amazon Web Services (AWS):** Siapa yang tidak tahu AWS? Infrastuktur cloud mereka dikelola dengan sangat baik, tetapi secara umum, biaya mereka adalah yang paling mahal. Mulai dari $38,56/bulan dan bandwidthnya (pada paket termurahnya) sangat terbatas (2GB) dibandingkan ketiga provider di atas.

- **Google Cloud Platform (GCP):** Diklaim lebih baik dari AWS dengan biaya yang sedikit lebih murah. Mulai dari $37,33/bulan dan bandwidth (pada paket termurahnya) juga sangat terbatas (2GB).

Selain itu, Cloudways tidak hanya bekerja untuk website WordPress saja, tetapi juga bisa Anda gunakan untuk meng-hostingkan website Magento, Drupal, Prestashop, Joomla, website PHP (Laravel, Codeignitor, Slim, Moodle), dan lain-lain.

Itu artinya, Cloudways dapat Anda gunakan untuk segala platform, apapun kebutuhan Anda.

#### 2. Control Panel yang Inovatif

Cloudways membuat custom control panel yang unik, berbeda dari provider lainnya.

Kesan pertama saya adalah, sejujurnya control panel buatan Cloudways terlihat agak _technical_, terutama untuk pemula yang baru datang dari shared hosting dan tidak pernah melihat control panel selain [cPanel hosting](https://penasihathosting.com/direktori/kategori/cpanel-hosting/), [DirectAdmin](https://penasihathosting.com/direktori/kategori/directadmin-hosting/) dan [Plesk](https://penasihathosting.com/direktori/kategori/plesk-hosting/).

Maksud saya, control panelnya memang terlihat intuitif tapi mungkin lebih ditujukan kepada developer.

Jika Anda datang dari [shared hosting](https://penasihathosting.com/direktori/kategori/shared-hosting/), saya rasa Anda perlu beberapa waktu untuk mengenal semua fungsi yang ada hingga terbiasa menggunakannya.

Beberapa tampilan control panel nya dapat Anda lihat pada tab dibawah ini:

**DASHBOARD:**

Cloudways baru saja membuat interface yang baru. Lebih bersih karena banyak white space, kemudian juga lebih intuitif dari sebelumnya, meskipun jika Anda datang dari shared hosting dan hanya mengenal [cPanel](https://penasihathosting.com/panduan-cpanel/), Anda tetap masih butuh penyesuaian.

![Dashboard cloudways terbaru](https://img.penasihathosting.com/2025/May/Dashboard-Cloudways-terbaru.webp "dashboard cloudways terbaru")

**SERVER:**

Ini adalah halaman server management.

Anda bisa mengatur hal-hal yang berkaitan dengan server Anda, seperti mengatur security, backups, SMTP, downgrade/upgrade spesifikasi server, dan lain-lain.

![server](https://img.penasihathosting.com/2025/May/Server.webp "server")

**APPLICATION:**

Ini adalah halaman 'Application Management'. Di halaman ini, Anda bisa melakukan tugas harian, seperti melihat statistik website pada bagian monitoring, melakukan backup dan restore, mengaktifkan CloudwaysCDN, membuat staging website, domain management, security, dan lain-lain.

![server management](https://img.penasihathosting.com/2025/May/Server-management.webp "server management")

**PROJECTS:**

Ini adalah halaman projects.

Dashboard Cloudways sangat ramah untuk para developer dengan (salah satunya) karena adanya fitur ini.

Jika Anda memiliki banyak klien atau mungkin Anda memiliki banyak website, Anda bisa merapihkan nya dengan memberikan nama project, sebagai contoh: semua website test Cloudways saya masukkan dalam project: Penasihat Hosting.

![projects](https://img.penasihathosting.com/2025/May/Projects.webp "projects")

**TEAM:**

Ini adalah halaman Team.

Di halaman ini, Anda dapat menambahkan anggota sehingga dapat berkolaborasi dalam team.

Anda dapat mengatur izin akses dari setiap anggota team Anda.

Bagusnya, di Cloudways Anda mendapatkan semua fitur termasuk fitur "Team" bahkan jika Anda hanya punya 1 server saja dan server paling murah. Berbeda dengan kebanyakan CloudPanel lain seperti RunCloud dimana Anda harus memilih paket Agency untuk mendapatkannya.

![Team-Cloudways](https://img.penasihathosting.com/2025/May/Team-Cloudways-1024x699.webp "team cloudways")

#### 3. Harga yang Lebih Murah, Hanya Saja...

Saya tahu, biaya managed cloud hosting jauh lebih mahal daripada biaya di shared hosting.

Di shared hosting, umumnya Anda mengeluarkan uang kurang dari Rp 50.000/bulannya, tapi di managed cloud hosting, pilihan terendahnya adalah di sekitar Rp 150.000/bulan.

Mungkin biaya menjadi satu hal yang paling Anda pertimbangkan. Namun bagi Anda yang serius menjalankan bisnis online, hosting adalah investasi terbaik.

![harga terbaru cloudways](https://img.penasihathosting.com/2025/May/harga-terbaru-Cloudways.webp "harga terbaru cloudways")

Biaya terendah di Cloudways adalah $11/bulannya dengan pilihan provider cloud hosting Digital Ocean Standard. Anda akan mendapatkan jumlah sumber daya sbb:

- RAM 1GB

- Processor 1 Core

- Storage 25GB

- Bandwidth 1TB

Dengan penawaran resources sebesar itu, saya rasa Anda bisa meng-host hingga 3 website dengan traffic _low to medium._

Hanya saja... Setelah saya banding-bandingkan dengan pesaing Cloudways seperti RunCloud, harga Cloudways **hanya lebih murah jika Anda berlangganan paket termurah nya saja.**

#### 4. Memliki Fitur-fitur yang Kaya

Tidak banyak perusahaan hosting yang mampu menjaga keseimbangan antara harga dan fungsionalitas. Cloudways adalah salah satunya.

Cloudways hadir dengan fitur-fitur yang kaya dan sangat dibutuhkan untuk menunjang aktifitas harian. Semua bisa dilakukan langsung dari control panel Cloudways.

Mulai dari:

1. Melihat Informasi Detail Akses Website/Aplikasi Anda

Seperti detail login WordPress Anda (Anda tidak perlu takut lupa atau data login aplikasi Anda terhapus), FTP, akses ke MySQL/Database, dan lain-lain lengkap dalam satu bagian.

![](https://img.penasihathosting.com/2025/May/Server-management-1024x673.webp)

2. Menambah aplikasi baru

Dengan fitur ini, Anda bisa menambahkan aplikasi atau website atau WordPress baru dengan domain yang berbeda ke dalam server. Berapa banyak website yang bisa Anda tambahkan dalam sebuah server? Jawabannya tergantung dari spesifikasi server yang Anda pilih.

![Menambahkan aplikasi di Cloudways](https://img.penasihathosting.com/2025/May/Menambahkan-aplikasi-1024x470.webp)

3. Meng-install SSL gratis ataupun custom SSL

Anda juga bisa mengatur nya supaya bisa diperpanjang secara otomatis

![Install SSL gratis di Cloudways](https://img.penasihathosting.com/2025/May/Install-SSL-gratis-1024x653.webp)

4. Melakukan backup dan restore dengan hanya 1x klik.

Anda tidak perlu menginstal plugin hanya untuk kebutuhan backup di website Anda.

Cloudways akan mem-backup data website Anda setiap hari dan menyimpan data backup nya selama 10 hari.

Anda juga bisa melakukan backup secara manual.

Sayangnya, backup ini berbayar $0.033/GB per server (saya akan membahasnya lebih detail pada bagian kekurangan nantinya).

![Fitur backup di Cloudways](https://img.penasihathosting.com/2025/May/backup-1024x708.webp)

5. Menambah atau meningkatkan ukuran server

Ketika website Anda tumbuh besar dan ukuran server Anda saat ini tidak lagi cukup untuk meng-handle kebutuhan website Anda, maka Anda perlu menambah ukurannya dengan melakukan vertical scaling.

Di Cloudways, Anda bisa melakukannya dengan mudah hanya dengan 1x klik.

Digital Ocean hanya bisa melakukan upgrade, sementara AWS dan GCP menawarkan opsi untuk bisa melakukan upgrade dan downgrade.

![Vertical scaling di Cloudways](https://img.penasihathosting.com/2025/May/Vertical-scaling-1024x547.webp)

6. Fitur staging/clone website untuk kebutuhan pengembangan

Anda juga bisa [membuat website](https://penasihathosting.com/cara-membuat-website/) clone atau website staging dengan hanya 1x klik di Cloudways.

Cloudways bahkan terus melakukan improvment terhadap fitur ini, baru-baru ini mereka melakukan upgrade fitur staging nya ke versi 2.0.

Di "Staging Managemenet" Anda bisa melakukan:

- PUSH = Mendorong perubahan yang Anda lakukan dari website staging ke website 'live' Anda.

- PULL = Mendorong perubahan pada website live ke staging

- DEPLOYMENT LOGS = Melihat log push dan pull yang sudah Anda lakukan

![Staging management di Cloudways](https://img.penasihathosting.com/2025/May/staging-management-1024x588.webp)

7. Recover Server / Application

Menurut saya, ini adalah fitur yang menjadi poin lebihnya Cloudways, karena cloud panel atau platform lain belum punya fitur ini.

Mungkin lebih tepatnya hanya sebuah platfrom yang bisa membuat fitur ini.

Jika Anda secara tidak sengaja menghapus aplikasi atau bahkan server di Cloudways, Anda bisa memulihkannya.

Cloudways akan menyimpan server dan aplikasi yang telah Anda hapus selama 15 hari.

![delete server 1](https://img.penasihathosting.com/2025/May/delete-server-1.webp "delete server 1")

Fitur ini sangat menghemat waktu, karena Anda tidak perlu meminta bantuan ke team support Cloudways yang biasanya memakan waktu 8 hingga 24 jam.

![Fitur recover server di Cloudways](https://img.penasihathosting.com/2025/May/Recover-Server-1024x335.webp)

8. Berbagai fungsi penting yang ditempatkan dalam satu bagian

Anda pasti akan menyukai yang ini. Cloudways memiliki berbagai fitur atau fungsi yang bisa Anda aktifkan atau non aktifkan hanya dengan 2 kali klik saja.

Fungsi-fungsi seperti Varnish, HTTP Redirection (bisa menggantikan plugin realy simple SSL), XMLPRC access dan lain-lain.

![Fitur aplikasi di Cloudways](https://img.penasihathosting.com/2025/May/Fitur-aplikasi-Cloudways.webp "fitur aplikasi cloudways")

Dan masih banyak fitur yang tersedia di Cloudways yang tidak mungkin saya sebutkan semuanya dalam review ini. Anda bisa meng-explore semuanya nanti ketika sudah mendaftar.

Ingat, Anda dapat mencoba Cloudways gratis/trial selama 3 hari.

[Coba Cloudways trial 3 hari](https://penasihathosting.com/go/cloudways)

_Discount 30% selama 2 bulan dengan memasukkan kode promo: **PHCW30**_

Namun, pastikan Anda mengetahui perbedaan server dan aplikasi. Biasanya untuk pemula sulit membedakan fungsi keduanya.

![beda server dan application](https://img.penasihathosting.com/2025/May/beda-server-dan-application-1024x473.webp "servers dan applications")

Keduanya memang memiliki entitas yang berbeda dimana di _applications_ Anda bisa melakukan tugas-tugas yang langsung berhubungan dengan aplikasi/website (misalnya WordPress) Anda, seperti backup, restore, install CDN, SSL, FTP, dan lain-lain, sedangkan di _servers_ adalah tentang pengaturan server secara umum.

### 5\. Memiliki Performance yang Sangat Mengesankan

Untuk mengetahui seberapa baik performance Cloudways, saya melakukan pengujian _Load Testing._

Saya akan membandingkan performance dari Digital Ocean yang Standard dan Premium.

**Digital Ocean Standard:** $11/bulan  
**Digital Ocean Premium:** $14/bulan  
**Konfigurasi:** Clean install (no cloudways optimization) + tidak menggunakan plugin _caching_ dan varnish juga di non aktifkan. Jadi, murni untuk menguji performa server setelah diinstall di platform Cloudways.

Website test diatas saya install WordPress dengan pengaturan default.

Dan untuk pengujian performanya, saya lakukan Load Test menggunakan tool [BlazeMeter](http://blazemeter.com/).

Oia, mohon dicatat bahwa dalam pengujian ini saya tidak memasukkan Amazon Web Services (AWS) dan Google Cloud Platform (GCP) dalam pengujian karena saya percaya bahwa Anda belum membutuhkan hosting dengan biaya yang mahal, kecuali Anda tahu alasan mengapa Anda perlu menggunakan AWS atau GCP.

Saya melakukan pengujian Load Testing dengan mengirimkan 20 virtual user untuk menjelajahi website test dalam durasi 5 menit.

Hasilnya?

**DIGITAL OCEAN STANDARD:**

Rata-rata response times nya adalah 1 second.

![new test do standard cloudways](https://img.penasihathosting.com/2025/May/New-Test-DO-Standard-Cloudways.webp "new test do standard cloudways")

**DIGITAL OCEAN PREMIUM:**

Rata-rata response times nya adalah 947,98 ms.

![cloudways do premium load test](https://img.penasihathosting.com/2025/May/Cloudways-DO-Premium-Load-Test.webp "cloudways do premium load test")

Agar lebih mudah dimengerti, saya rangkum hasil nya dalam tabel dibawah ini:

| Fitur                   | Digital Ocean Standard | Digital Ocean Premium |
| ----------------------- | ---------------------- | --------------------- |
| Average Response Times  | 1 second               | 947,98 ms             |
| Average Throughput      | Perlu Data Spesifik    | Perlu Data Spesifik   |
| Average Bandwidth       | Perlu Data Spesifik    | Perlu Data Spesifik   |

Menurut data di atas, Digital Ocean Premium memiliki performa yang lebih baik daripada Digital Ocean Standard dalam semua aspek yang diuji: average response times, average throughput, dan average bandwidth

Keduanya menunjukkan performa yang baik dengan waktu respon di bawah 1 detik. Hal ini menunjukkan bahwa server mampu menangani permintaan dengan efisien, meskipun tanpa optimisasi dari Cloudways atau penggunaan plugin caching dan varnish.

DO Standard memiliki waktu respon rata-rata 1 detik. Sedangkan, DO Premium sedikit lebih cepat dengan waktu respon rata-rata 947,98 ms. Perbedaan ini mungkin tidak signifikan bagi pengguna biasa, tetapi untuk situs dengan traffic yang sangat tinggi, perbedaan kecil ini bisa berarti banyak.

Penting juga untuk mencatat bahwa tidak ada error yang terjadi selama pengujian, menunjukkan stabilitas dan kehandalan kedua jenis server ini.

Secara keseluruhan, berdasarkan hasil tes ini, baik DO Standard maupun Premium menawarkan performa yang solid dan dapat diandalkan. Namun, jika kecepatan respon adalah prioritas utama, DO Premium dapat menjadi pilihan yang lebih baik, meskipun perbedaannya tidak begitu besar.

Bagaimana dengan uptime nya? _Well_, menurut saya hanya menghabiskan waktu hanya untuk menguji seberapa baik rata-rata uptime ketiga provider ini, karena saya yakin server mereka _reliable_. Anda bisa membuktikannya sendiri setelah mencobanya.

Ohia, saya pernah menguji uptime ketiga provider pada tahun 2021 menggunakan Pingdom selama 2 bulan. Hasilnya? Rata-ratanya adalah 99,98% - 100%.

### 6\. Dapat Menambahkan Anggota Ke Dalam Team

Jika Anda bekerja secara team, Anda akan menyukai fitur yang satu ini.

Cloudways memudahkan Anda untuk menambah anggota a ke dalam team Anda. Mereka bisa mengakses account Cloudways Anda dan Anda bisa memberikan izin akses untuk tiap-tiap anggota.

Misalnya:

- **Billing Access:** Hanya bisa mengakses fungsi-fungsi yang berhubungan dengan billing

- **Support Access:** Akan memberikan akses dimana anggota team dapat membuat atau mengelola tiket support

- **Console Access:** Anda bisa memberikan akses penuh kepada anggota team atau memberikan akses terbatas hanya pada server atau aplikasi tertentu saja.

Bagusnya, fitur ini bisa Anda nikmati apapun paket yang Anda ambil. Fitur ini juga merupakan poin plus, karena banyak provider lain yang tidak memiliki fitur ini atau justru mengharuskan Anda untuk mengambil paket yang lebih mahal agar mendapatkan fitur team member.

![](https://img.penasihathosting.com/2025/May/Team-Cloudways-1024x699.webp)

### 7\. Gratis Trial Hosting 3 Hari dan Gratis Migrasi Hosting

Jika Anda ingin mencoba-coba Cloudways terlebih dahulu, Anda bisa mendaftar tanpa perlu membayar dengan memanfaatkan trial selama 3 hari.

Jika Anda merasa puas, Anda bisa upgrade account Anda dari trial ke full setelah nya.

Dan kabar baiknya, Cloudways juga menawarkan [migrasi dari hosting lama Anda ke Cloudways](https://penasihathosting.com/tutorial-migrasi-wordpress-ke-cloudways/) secara gratis untuk satu website.

[Coba Cloudways trial 3 hari](https://penasihathosting.com/go/cloudways)

_Discount 30% selama 2 bulan dengan memasukkan kode promo: **PHCW30**_

### 8\. Sistem Pembayarannya _Pay As You Go Service_ 

Cloudways menganut sistem pembayaran **Pay As You Go Service**, yang artinya Anda hanya membayar untuk sumber daya yang Anda gunakan saja.

Tidak ada kontrak yang mengikat dimana Anda harus berlangganan secara bulanan ataupun tahunan. Dengan kata lain Anda dapat berhenti kapan saja Anda mau. _No worries!_

![add funds di cloudways](https://img.penasihathosting.com/2025/May/add-funds-di-Cloudways-1024x578.webp "metode pembayaran di Cloudways")

**Bagaimana dengan metode pembayarannya?**

Untungnya, Cloudways menerima metode pembayaran via PayPal.

Selain itu, mereka juga menerima pembayaran via prepaid card, seperti Payoneer dan Wise (kartu yang saya gunakan untuk pembayaran online). Jika Anda tidak punya keduanya, Anda dapat menggunakan prepaid card yang dikeluarkan oleh Bank Jago Syariah.

Cara untuk mendaftar Bank Jago Syariah sangat mudah. Tidak perlu ke bank. Semuanya bisa dikerjakan online. Saya juga salah satu penggunanya dan terbantu dengan adanya prepaid card yang dikeluarkan oleh Bank Jago Syariah.

## Kekurangan Menggunakan Cloudways

Setidaknya ada beberapa kekurangan yang perlu Anda perhatikan sebelum memutuskan apakah Cloudways adalah pilihan yang tepat untuk Anda.

#### 1. Peningkatan Kecepatan _User Interface_ yang Baru Belum Optimal

Meski sudah memperkenalkan _interface_ yang baru, Cloudways belum mampu menandingi kecepatan pesaing utamanya, Runcloud. Saya tetap merasa kurang puas karena peningkatan kecepatan pada _interface_ yang baru tidak begitu signifikan bila dibandingkan dengan _interface_ klasik mereka.

Tapi, _interface_ yang baru, lebih modern dan lebih intuitif ini masih dalam tahap Beta dan mudah-mudahan mereka memfokuskan pengembangan untuk bagaimana membuatnya lebih cepat sehingga bisa membuat bekerja menjadi lebih produktif dan nyaman.

### 2\. Cloudways Memiliki Support yang Baik, Sayangnya Memiliki Keterbatasan-Keterbatasan

Cloudways menyediakan support via ticket dan live chat 24/7/365.

Dimana live chat dapat Anda gunakan untuk meminta bantuan yang tidak membutuhkan inspeksi yang detail. Saya kira permasalahan seperti: masalah pada SSL, masalah pada backup, install CDN, Add on, dan semacamnya saja.

Sedangkan jika Anda membutuhkan bantuan yang membutuhkan inspeksi yang dalam, maka Anda bisa menggunakan tiket.

Berdasarkan pengujian saya, response support via live chat nya sangat cepat. Sayangnya, support (gratis) dari Cloudways ini memiliki keterbatasan-keterbatasan, seperti:

1. Durasi live chat dibatasi hanya 20 menit saja, sedangkan waktu response tiket adalah 3 jam.

3. Support via live chat mungkin tidak langsung ditangani oleh seorang agent, melainkan akan dimulai dari robot/bot terlebih dahulu.

5. Support gratis nya (standard support) hanya untuk hal-hal yang berhubungan dengan platform saja, seperti install SSL, menambahkan server/aplikasi, backups, cloning server dan lain-lain. Jadi, saya tidak bisa merekomendasikan nya untuk semua orang.

7. Adapun jika Anda membutuhkan support yang berhubungan dengan aplikasi Anda, misalnya error pada website WordPress, maka mereka tidak akan langsung membantu, tetapi hanya akan memandu Anda bagaimana caranya memecahkan masalah Anda, tetapi Anda sendiri yang mengotak-ngatiknya.

Karena keterbatasan-keterbatasan diatas itulah, saya tidak bisa merekomendasikan Cloudways untuk semua pengguna.

![Scope support Cloudways](https://img.penasihathosting.com/2025/May/Scope-support-Cloudways-1024x839.webp "Scope support Cloudways")

### 3\. Fitur Backup Otomatis Cloudways Tidak Gratis. Anda Harus Bayar $0,033/GB per Backup

Cloudways menggunakan off-site backup dimana file website Anda akan di backup di jaringan penyimpanan eksternal, yang berbeda dengan jaringan penyimpanan website Anda.

Tetapi, off-site backup ini dikenakan biaya, yaitu $0,033/GB.

Dan hal lain yang perlu Anda ketahui adalah mereka membulatkan nominal charge backup nya ke kelipatan $0,5. Misalnya, biaya backup Anda bulan ini adalah $1,22, maka akan dibulatkan menjadi $1,5, dst...

![Backup fee Cloudways](https://img.penasihathosting.com/2025/May/Backup-fee-Cloudways-1024x158.webp "Backup fee Cloudways")

### 4\. Tidak Mendapatkan Root Access

Clouways adalah sebuah platform, semua server dikelola oleh pusat sehingga saling terkoneksi sama lain.

Jadi, semua pengguna tidak mungkin punya akses ke root server, karena tidak ada jaminan mungkin saja ada pengguna yang merusak _tools_ tertentu yang merupakan inti dari platform Cloudways.

Jika Anda menginginkan Root Access, Anda dapat mencoba alternatif lain seperti [Runcloud](https://penasihathosting.com/review-runcloud/).

### 5\. Cloudways Tidak Menjual Domain

Cloudways tidak menjual domain.

Jadi, Anda perlu [membeli domain](https://penasihathosting.com/direktori/kategori/domain/) di tempat lain, seperti [Namecheap](https://penasihathosting.com/go/namecheap) (affiliate), GoDaddy atau bisa juga melalui provider lokal.

### 6\. Tidak ada Email Hosting

Jika sebelumnya Anda menggunakan [email hosting](https://penasihathosting.com/direktori/kategori/email-hosting/) dari webmail di cPanel (shared hosting), Anda tidak akan menemukan yang serupa di Cloudways.

Sebagai gantinya, Cloudways menyediakan layanan email dari pihak ketiga. Bentuknya sebagai add on, jadi berbayar dan sifatnya opsional.

Untuk transactional email bisa menggunakan [Elastic](https://support.cloudways.com/how-to-activate-elasticemail-addon/?utm_source=Platformkb&utm_medium=kbsearch), sedangkan mailbox menggunakan [Rackspace](https://support.cloudways.com/how-to-use-rackspace-addon/?utm_source=Platformkb&utm_medium=kbsearch) dengan harga yang lebih terjangkau dibandingkan layanan email lainnya.

![add on cloudways](https://img.penasihathosting.com/2025/May/Add-on-Cloudways-1024x681.webp "layanan email di Cloudways")

## Kesimpulan: Apakah Saya Merekomendasikan Cloudways?

Saya merekomendasikan Cloudways, tapi tidak untuk semua model pengguna.

Agar memudahkan Anda dalam pengambilan keputusan, saya mencoba merinci beberapa model pengguna yang mungkin cocok menggunakan Cloudways:

**Saya merekomendasikan Cloudways untuk enam tipe pengguna berikut:**

- Jika Anda pemula, sebelumnya dari shared hosting, mengerti menggunakan cPanel, maka Cloudways cocok untuk Anda.

- Jika Anda seorang Google _Expert_ (terbiasa mencari masalah Anda sendiri atau minimal bisa mengikuti panduan yang diberikan oleh staf support Cloudways terhadap masalah teknis pada website Anda), Cloudways cocok untuk Anda.

- Jika Anda hanya ingin meng-hostingkan satu atau beberapa website dengan _traffic low to medium_, menginginkan harga managed cloud VPS hosting termurah, maka paket Cloudways + Digital Ocean seharga $11 (atau $14 dengan Premium) adalah pilihan yang tepat untuk Anda

- Jika sebelumnya Anda menggunakan SiteGround, Cloudways adalah alternatif terbaiknya.

- Jika sebelumnya Anda menggunakan premium managed WordPress hosting seperti Kinsta dan WP Engine atau beberapa pilihan managed cloud hosting lokal, dan menginginkan harga yang tidak jauh berbeda, tetapi dengan performance yang lebih baik dan storage + bandwidth yang lebih besar, maka Cloudways cocok untuk Anda.

- Alternatif terbaik untuk Anda yang tidak mau menggunakan cloud panel seperti RunCloud, SpinupWP, ServerPilot, dan lain-lain, karena ribet mengurus dua account.

[Kunjungi Cloudways (Gratis trial 3 hari)](https://penasihathosting.com/go/cloudways)

_Discount 30% selama 2 bulan dengan memasukkan kode promo: **PHCW30**_

**Dan saya tidak bisa merekomendasikan Cloudways untuk beberapa model pengguna berikut:**

- Jika Anda total-newbie, baru datang dari shared hosting, tidak mengerti cPanel atau website, maka Cloudways tidak cocok untuk Anda.

- Cloudways tidak memberikan **root access**, jadi untuk **system experts** yang gemar mengotak-ngatik konfigurasi pada server, Cloudways tidak akan cocok untuk Anda.

- Jika Anda adalah agency yang mengelola banyak website, maka Cloudways bukanlah pilihan yang tepat, karena harganya mahal untuk pengelolaan banyak website. Saya lebih merekomendasikan RunCloud untuk agency. Atau Anda boleh mencobanya terlebih dahulu dan putuskan setelahnya.

**Artikel lainnya:**

- [Tutorial Lengkap Cara Install WordPress di Cloudways](https://penasihathosting.com/install-wordpress-cloudways/)
