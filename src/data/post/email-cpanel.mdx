---
title: "Pengaturan Email Account di cPanel: Panduan Lengkap"
publishDate: 2016-12-13
updateDate: 2025-05-21
categories: 
  - "Panduan Lengkap"
tags: 
  - "panduan-cpanel"
  - "cpanel"
  - "email"
  - "hosting"
image: https://img.penasihathosting.com/2025/cpanel/email-cpanel.webp
excerpt: "Cara membuat dan mengelola email dengan domain sendiri di cPanel. Panduan lengkap untuk mengatur email, webmail, forwarding, dan spam filters."
metadata:
  title: "Pengaturan Email Account di cPanel: Panduan Lengkap (Edisi 2025)"
  description: "Pelajari cara membuat dan mengelola email dengan domain sendiri, mengakses webmail, mengatur forwarding, dan mengonfigurasi spam filters di cPanel dengan informasi terbaru tahun 2025."
  guideId: "cpanel-guide"
  chapterIndex: 5
  chapterTitle: "Pengaturan Email Account"
---

# Pengaturan Email Account di cPanel: Panduan Lengkap

Setelah Anda memahami cara [mengelola domain di cPanel](/domain-cpanel/), langkah selanjutnya adalah mempelajari cara mengatur email account. Salah satu keuntungan memiliki hosting dan domain sendiri adalah kemampuan untuk membuat alamat email dengan domain Anda sendiri, yang terlihat lebih profesional dibandingkan layanan email gratis.

## Pengenalan Email di cPanel

Bagian "Email" pada cPanel memungkinkan Anda untuk mengelola segala aspek yang berhubungan dengan email Anda:

- Mulai dari membuat email baru,
- Membuat autoresponders email
- Hingga mengaktifkan "SpamAssassin" guna mencegah email spam.

Ah, iya..

Berita baiknya, Anda dapat membuat email accounts menggunakan nama domain Anda sendiri seperti "<EMAIL>", jadi Anda tidak akan lagi menggunakan alamat email yang <NAME_EMAIL>.

![Bagian Emails di cPanel](https://img.penasihathosting.com/2025/cpanel/Emails-1024x943.webp)

Di bagian Email, Anda akan menemukan beberapa fitur utama:

1. **Email Accounts** - Untuk membuat dan mengelola akun email
2. **Forwarders** - Untuk meneruskan email ke alamat lain
3. **Email Filters** - Untuk mengatur filter email
4. **Autoresponders** - Untuk mengatur balasan otomatis
5. **Spam Filters** - Untuk mengonfigurasi filter spam
6. **Mailing Lists** - Untuk membuat dan mengelola mailing list
7. **Global Email Filters** - Untuk mengatur filter email global
8. **Email Deliverability** - Untuk memeriksa status pengiriman email

Mari kita bahas masing-masing fitur secara detail.

## Membuat Email Account

Klik "Email Accounts" pada cPanel Anda seperti yang terlihat pada _screenshoot_ diatas untuk membuat email account dengan nama domain Anda sendiri.

Di halaman "Email Accounts" seperti gambar dibawah ini, perhatikan yang saya lingkari warna merah. Dimana:

- ♾️ Available adalah jumlah email yang bisa Anda buat di hosting Anda. Jumlah nya tentu berbeda-beda tergantung dari paket hosting yang Anda pilih. Jika menunjukan ♾️, maka Anda bisa membuat akun email yang sifatnya "tidak terbatas" (dalam tanpa kutip).

- Dan 0 used adalah jumlah account yang sudah Anda gunakan. Dalam contoh saya dibawah ini, saya sama sekali belum membuat account email jadi jumlah nya masih 0.

![Halaman Email Accounts di cPanel](https://img.penasihathosting.com/2025/cpanel/Membuat-akun-email-di-cPanel-1024x841.webp)

Nah, untuk membuat account email, klik tombol "+ CREATE" yang saya tunjuk pada gambar diatas.

Kemudian Anda hanya perlu mengisi form-form yang telah disediakan, dari atas sampai bawah:

![Formulir membuat email account baru di cPanel](https://img.penasihathosting.com/2025/cpanel/Membuat-email-account-1024x956.webp)

- Di kolom "Username", masukkan alamat email atau nama email yang Anda inginkan - sebagai contoh "Admin" atau "namaAnda".

- Anda bisa membiarkan pilihan 'Set password now'

- Tulis password pada kolom "Password". Pastikan Anda membuat password yang kuat. Sekali lagi, saya menyarankan untuk menggunakan password generator (klik GENERATE).

- Terakhir tentukan storage space email Anda (klik tombol "Edit Settings" untuk membuka pilihannya), misalnya 100 MB, 200MB, 1000MB atau unlimited. Perlu Anda ketahui bahwa space pada email ini akan mengurangi space pada account hosting Anda, jadi mungkin Anda tidak akan memberikan orang lain akses yang unlimited.

- Klik tombol "Create"

Ketika Anda telah membuat email baru, Anda akan menemukan bahwa email yang baru Anda buat akan terdaftar pada list email accounts dibawah ini:

![Daftar email accounts yang telah dibuat di cPanel](https://img.penasihathosting.com/2025/cpanel/Akun-email-telah-berhasil-dibuat-1024x718.webp)

Pada daftar email accounts diatas, Anda dapat mengatur segala hal yang berhubungan dengan email account Anda, termasuk password, quota, delete dan mail client.

### Praktik Terbaik untuk Email Account

Berikut beberapa praktik terbaik saat membuat dan mengelola email account:

1. **Gunakan nama yang profesional** - Pilih username yang profesional dan mudah diingat
2. **Buat password yang kuat** - Gunakan kombinasi huruf, angka, dan karakter khusus
3. **Atur quota yang sesuai** - Sesuaikan quota dengan kebutuhan dan kapasitas hosting
4. **Buat akun terpisah** - Buat akun terpisah untuk fungsi yang berbeda (admin, info, support, dll)
5. **Backup email secara berkala** - Gunakan email client untuk mengunduh email penting

## Mengakses Webmail

Jadi, sekarang Anda sudah membuat email dengan nama domain Anda sendiri.

Lalu bagaimana cara log in/akses ke webmailnya?

**Ada dua cara:**

Pertama, pada fitur "Email Accounts" di cPanel, klik tombol "Check Email"

![Tombol Check Email untuk mengakses webmail di cPanel](https://img.penasihathosting.com/2025/cpanel/Check-Email-1024x539.webp)

Kedua, ketik url di bawah ini pada browser Anda:

**http://domainanda.com/webmail**

(ganti '**domainanda**' dengan nama domain Anda)

Kemudian masukkan alamat email dan password yang sudah Anda buat sebelumnya.

Setelah Anda sukses melakukan log in, Anda akan diarahkan ke halaman webmail dengan tampilan baru update versi cPanel 108 seperti dibawah ini:

![Tampilan antarmuka webmail Roundcube di cPanel](https://img.penasihathosting.com/2025/cpanel/Akses-webmail-1024x955.webp)

Hanya ada satu pilihan webmail client pada versi cPanel 108, yaitu rouncube.

Oia, Anda juga bisa mengakses webmail client ini via mobile. Pengaturan nya berbeda-beda tergantung dari device yang Anda gunakan.

### Menggunakan Email Client Desktop

Selain menggunakan webmail, Anda juga dapat mengonfigurasi email client desktop seperti:

1. **Microsoft Outlook**
2. **Mozilla Thunderbird**
3. **Apple Mail**
4. **eM Client**

Untuk mengonfigurasi email client, Anda memerlukan informasi berikut:

- **Alamat email** - <EMAIL>
- **Username** - <EMAIL> (biasanya sama dengan alamat email)
- **Password** - Password email Anda
- **Server masuk (IMAP)** - mail.domainanda.com (Port: 993, SSL: Ya)
- **Server keluar (SMTP)** - mail.domainanda.com (Port: 465, SSL: Ya)

Informasi server yang tepat dapat ditemukan di bagian "Email Accounts" di cPanel, klik "Connect Devices" pada akun email yang ingin Anda konfigurasikan.

## Forwarding Email Anda ke Alamat Email yang Lain

Katakanlah Anda ingin email yang <NAME_EMAIL> diteruskan/forward ke alamat email lain yang Anda miliki - seperti <EMAIL>.

Pertama-tama, klik "Forwarders" pada cPanel Anda, kemudian klik "Add Forwarders" untuk sampai kehalaman dibawah ini:

![Halaman Forwarders email di cPanel](https://img.penasihathosting.com/2025/cpanel/Forwarders-913x1024.webp)

Disini Anda perlu memasukkan alamat email yang sudah Anda buat sebelumnya (<EMAIL>) pada kolom "Address to Forward" dan memilih destinasi forwardnya pada kolom "Forward to Email Address".

> Perlu Anda ketahui bahwa pengaturan forwarding email ini hanya untuk forwarding saja, maksudnya ketika Anda membalas <NAME_EMAIL>, artinya pesan dibalas dari email tersebut, bukan dari email yang Anda buat pada cPanel (<EMAIL>).

### Jenis-jenis Forwarding

Ada beberapa jenis forwarding yang dapat Anda konfigurasikan:

1. **Forward to Email Address** - Meneruskan email ke alamat email lain
2. **Forward to System Account** - Meneruskan email ke akun sistem di server
3. **Discard with Message** - Membuang email dan mengirim pesan ke pengirim
4. **Pipe to a Program** - Meneruskan email ke program atau skrip

## Mengatur Spam Filters

Pada umumnya, cPanel memberikan Anda sebuah tool untuk mem-filter email-email spam, yaitu Apache SpamAssassin™, Namun mungkin Anda akan menjumpai tool lain seperti SpamExperts Pro pada cPanel Anda.

Pada dasarnya kedua tool tersebut memiliki fungsi yang sama, namun biasanya "SpamExperts Pro" hanya diberikan apabila Anda berlangganan paket hosting tertentu saja. Umumya hanya tersedia pada paket hosting business/enterprise.

Karena umumnya cPanel menggunakan Apache SpamAssassin™, maka kali ini saya akan menjelaskan pengaturan tool tersebut saja.

Pertama-tama, klik Spam Filters pada bagian "Email" tampilan depan cPanel Anda.

![Menu Spam Filters di cPanel](https://img.penasihathosting.com/2025/cpanel/Spam-filters-di-cPanel-1024x950.webp)

Kemudian Anda akan diarahkan kehalaman dibawah ini:

![Halaman pengaturan Apache SpamAssassin di cPanel](https://img.penasihathosting.com/2025/cpanel/Pengaturan-spam-filters-cPanel-946x1024.webp)

Dihalaman tersebut, Anda akan dihadapkan pada beberapa pilihan:

- Pada umumnya, di cPanel terbaru pengaturan "Apache SpamAssassin" secara otomatis sudah diaktifkan, namun apabila Anda melihat belum diaktifkan, maka akan muncul tombol berwarna biru yang bertuliskan "Enable Apache SpamAssassin yang mana Anda harus mengklik tombol tersebut untuk mengaktifkan spam filters.

- Automatically Delete New Spam (Auto-Delete) apabila Anda ingin tidak hanya menandai email-email yang spam, namun juga menghapus secara otomatis email-email spam tersebut.

- Anda juga dapat mengatur seberapa sensitif filter spam pada skala 1-10, dimana skala 1 adalah paling liberal dan skala 10 menjadi paling konservatif.

Terakhir Anda juga dapat memblacklist atau melindungi email-email tertentu dengan cara melakukan whitelist pada pengaturan "Configure Apache SpamAssassin™".

Tapi untuk saat ini (pertama kali) tinggalkan saja pengaturan tersebut, karena Anda belum membutuhkannya.

### Praktik Terbaik untuk Spam Filtering

Berikut beberapa praktik terbaik untuk mengonfigurasi spam filtering:

1. **Mulai dengan pengaturan default** - Jangan langsung mengubah pengaturan sensitivitas
2. **Periksa folder spam secara berkala** - Pastikan email penting tidak masuk folder spam
3. **Whitelist pengirim penting** - Tambahkan alamat email penting ke whitelist
4. **Sesuaikan sensitivitas secara bertahap** - Ubah sedikit demi sedikit dan pantau hasilnya
5. **Gunakan kombinasi filter** - Manfaatkan SpamAssassin dan filter email lainnya

## Fitur Email Lainnya di cPanel

Selain fitur-fitur yang telah dibahas di atas, cPanel juga menyediakan beberapa fitur email lainnya:

### Autoresponders

Autoresponders memungkinkan Anda untuk mengirim balasan otomatis ke email yang masuk. Ini berguna untuk:

- Pemberitahuan cuti atau liburan
- Konfirmasi penerimaan email
- Informasi kontak alternatif
- Jawaban atas pertanyaan umum

Untuk mengatur autoresponder:

1. Klik "Autoresponders" di bagian Email
2. Klik "Add Autoresponder"
3. Pilih akun email, tentukan interval, dan tulis pesan
4. Klik "Create/Modify"

### Email Filters

Email Filters memungkinkan Anda untuk mengatur aturan pemfilteran email berdasarkan kriteria tertentu. Anda dapat:

- Memindahkan email ke folder tertentu
- Menandai email sebagai dibaca
- Menghapus email
- Meneruskan email

Untuk mengatur filter email:

1. Klik "Email Filters" di bagian Email
2. Klik "Create a New Filter"
3. Tentukan aturan dan tindakan
4. Klik "Create"

### Mailing Lists

Mailing Lists memungkinkan Anda untuk membuat dan mengelola daftar email untuk komunikasi massal. Ini berguna untuk:

- Newsletter
- Pengumuman
- Diskusi grup
- Pemasaran email

Untuk membuat mailing list:

1. Klik "Mailing Lists" di bagian Email
2. Klik "Create a Mailing List"
3. Isi informasi yang diperlukan
4. Klik "Add"

## Kesimpulan

Mengatur email account di cPanel adalah langkah penting untuk membangun identitas online profesional. Dengan memiliki alamat email dengan domain sendiri, Anda dapat meningkatkan kredibilitas dan branding bisnis atau website Anda.

Pada [bab berikutnya](/file-manager-cpanel/), kita akan mempelajari cara mengelola file website di cPanel, termasuk cara menggunakan File Manager dan FTP.

<div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border-l-4 border-primary my-8">
  <h4 class="text-lg font-bold text-blue-800 dark:text-blue-300 mb-2">Tips Email Profesional</h4>
  <p class="text-sm text-gray-700 dark:text-gray-300">
    Buat beberapa alamat email <NAME_EMAIL>, <EMAIL>, dan <EMAIL> untuk memisahkan komunikasi berdasarkan tujuannya. Ini membuat pengelolaan email lebih terorganisir dan memberikan kesan profesional kepada penerima.
  </p>
</div>
