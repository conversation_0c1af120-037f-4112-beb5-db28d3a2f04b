---
title: "Review RunCloud"
publishDate: 2021-08-04
updateDate: 2025-05-12
category: "Review Hosting"
image: https://img.penasihathosting.com/2025/May/Review-RunCloud-1024x633.webp
tags:
  - "Cloud Panel"
excerpt: "Review lengkap RunCloud, cloud panel untuk mengelola Cloud VPS. Pelajari fitur, performa, harga, kelebihan dan kekurangan RunCloud untuk bantu Anda memutuskan."
metadata:
  title: "Review RunCloud: Managed Cloud Panel untuk Optimasi VPS"
  description: "Temukan kelebihan RunCloud sebagai cloud panel untuk VPS Anda. Bandingkan performanya, fitur, dan harga untuk mengetahui apakah RunCloud adalah pilihan tepat untuk kebutuhan hosting Anda."
---

Sedang mempertimbangkan menggunakan RunCloud untuk meng-hostingkan website WordPress Anda?

Atau sedang mempertimbangkan untuk upgrade/pindah dari [shared hosting](https://penasihathosting.com/direktori/kategori/shared-hosting/) ke [Cloud VPS](https://penasihathosting.com/direktori/kategori/unmanaged-vps/) untuk mendapatkan performa (uptime dan speed) yang jauh lebih baik?

Apapaun alasan Anda membaca review RunCloud ini, saya yakin Anda akan menemukan jawaban dari pertanyaan yang sedang Anda cari, karena saya akan bahas tentang RunCloud cukup detail pada review ini.

Saya bahkan memindahkan salah satu _money site_ saya ke Digital Ocean Managed by RunCloud sekitar 3 minggu lalu, sehingga review ini didukung oleh dua data yang bisa membantu Anda dalam pengambilan keputusan, yaitu data dari pengalaman pengguna dan data dari statistik (performance test).

Dan untuk mengawali semuanya, mari mulai dari mengenal apa itu RunCloud (Anda bisa skip bagian ini jika Anda sudah mengenal tentang RunCloud).

## Apa itu RunCloud?

RunCloud berasal dari Malaysia dan telah berdiri sejak tahun 2014. Sampai hari ini, mereka telah melayani lebih dari 200.000 aplikasi dan 400.000 domain dari seluruh dunia.

![RunCloud achievement](https://img.penasihathosting.com/2025/May/RunCloud-achievment.webp)

Jika sebelumnya Anda belum pernah bermain dalam [dunia VPS](https://penasihathosting.com/direktori/kategori/unmanaged-vps/), kemungkinan besar Anda akan bingung melihat apa yang RunCloud tawarkan, karena mereka melakukan pendekatan yang berbeda dari yang biasa Anda temui di [shared hosting](https://penasihathosting.com/direktori/kategori/shared-hosting/).

Yang perlu Anda ketahui pertama kali adalah RunCloud bukanlah provider hosting. Mereka bahkan tidak menyediakan hosting sama sekali.

Lalu apa yang RunCloud tawarkan?

## Apa yang RunCloud Tawarkan?

Secara singkat, RunCloud membuat manajemen server menjadi mudah.

Saya tahu, banyak pengguna menginginkan _high performance_ hosting seperti misalnya [Kinsta](https://penasihathosting.com/review-kinsta/) (Premium Managed WordPress Hosting), namun dengan harga yang jauh lebih murah.

Cloud VPS hosting seperti Digital Ocean, Vultr, Linode dan UpCloud adalah jawaban dari permasalahan diatas. Anda bisa mendapatkan hosting dengan performa yang tidak kalah dengan Premium [Managed WordPress Hosting](https://penasihathosting.com/direktori/kategori/wordpress-hosting/), tapi dengan harga yang jauh lebih terjangkau.

**Spoiler:**

![Setelah pindah ke RunCloud](https://img.penasihathosting.com/2025/May/Setelah-pindah-ke-RunCloud-1.webp)

_Saya sudah membuktikannya dengan membandingkan performa RunCloud (dengan VPS Digital Ocean seharga $12/bulan) dan Kinsta paket Business 1 (seharga $100/bulan). Dari monitoring salah satu website saya setelah dipindahkan ke RunCloud, load time nya bahkan lebih cepat (dengan new RunCloud Hub FastCGI caching) seperti yang ditunjukan pada grafik monitoring load time diatas ._

Tapi, masalah yang selalu menjadi momok bagi pengguna reguler (bukan developer) adalah Cloud VPS hosting terlalu rumit, membutuhkan waktu dan kedisiplinan untuk mempelajarinya.

RunCloud (salah satunya) menjawab permasalahan itu dengan baik.

Mereka membangun software, sebuah control panel hosting yang telah didesain khusus untuk mengelola [cloud hosting](https://penasihathosting.com/direktori/kategori/cloud-hosting/) dari provider-provider, seperti Digital Ocean, Vultr, Linode, Google Cloud, dan lainnya.

Anda tinggal menghubungkan provider VPS yang Anda pilih dan RunCloud akan mengatur konfigurasinya, sekuritinya, dan bahkan mengoptimalkannya.

Secara sederhana, Anda serahkan konfigurasi server pada RunCloud dan Anda tinggal mengatur pengaturan aplikasi Anda saja di dalam software atau control panelnya. Anda bisa install WordPress dengan mudah, mengakses file manager, melakukan backup dan restore seperti di [cPanel](https://penasihathosting.com/panduan-cpanel/), dan lain-lain.

## Review RunCloud

Sebelum menulis review ini, saya sudah menggunakan RunCloud sekitar hampir 3 minggu.

Saya pindahkan salah satu website saya dari Kinsta paket Business 1 ke RunCloud dengan cloud VPS hosting dari Digital Ocean (Premium AMD 2GB) untuk melihat apakah load time nya lebih cepat atau lebih lambat dari Kinsta (Premium Managed Hosting yang saya jadikan _sample_ dalam pengujian).

Saya juga melakukan pengujian dengan dua konfigurasi yang berbeda untuk melihat mana yang memiliki performance lebih cepat antara:

- RunCloud Nginx + RunCloud HUB dengan FastCGI Caching

- RunCloud OpenLiteSpeed + LiteSpeed Caching

Sehingga hasil pengujian dapat membantu Anda memutuskan apa konfigurasi yang lebih cepat untuk website WordPress Anda.

> Salah satu kekurangan review ini adalah saya hanya fokus memeriksa performa RunCloud menggunakan CMS WordPress saja dan mungkin tidak berlaku untuk CMS lainnya. Itu artinya, review ini tidak bersifat umum.

Pada review RunCloud ini, saya juga akan mencoba menjawab pertanyaan-pertanyaan yang mungkin sedang Anda cari-cari.

Pertanyaan-pertanyaan seperti:

1. Bagaimana cara kerja RunCloud?

3. Apa saja kelebihan dan kekurangan menggunakan RunCloud?

5. Apakah harga nya masuk akal?

7. Apakah RunCloud sudah dioptimalkan untuk website WordPress?

9. Bagaimana performa RunCloud dibandingkan pesaing-pesaingnya, seperti CloudWays, SpinupWP dan GridPane?

11. Dan RunCloud cocok untuk siapa saja? Apakah pengguna pemula (newbie) cocok menggunakan RunCloud?

Semua akan saya bahas dalam review ini.

## Kelebihan Menggunakan RunCloud

Setidaknya ada enam kelebihan RunCloud dalam catatan saya.

Yang paling saya sukai selain _performance_nya yang mengesankan adalah user interface nya.

### 1\. User Interface yang Cantik dan Intuitif

Saya sudah pernah menggunakan beberapa cloud panel, seperti RunCloud, GridPane, SpinupWP dan platform seperti CloudWays, tetapi diantara semua pilihan tersebut, RunCloud adalah favorit saya dari sisi user interfacenya.

User interfacenya cantik, rapih dan seperti terlihat mereka telah menghabiskan ribuan jam mulai dari meneliti, mendesain dan membangun nya.

![User Interface RunCloud](https://img.penasihathosting.com/2025/May/User-Interface-RunCloud.webp)

Cloud panelnya komprehensif. Anda bisa mengelola baik pengaturan server, maupun pengaturan website. Kedua pengaturannya tentu saja terpisah, Anda harus mengklik "Web Application" dalam halaman dashboard Server Anda untuk [mengelola website Anda](/jasa-maintenance-website/).

Jika Anda pernah menggunakan [CloudWays](https://penasihathosting.com/review-cloudways/), Anda pasti akan menemukan banyak sekali kemiripan dengan RunCloud dari segi fitur. Tetapi, control panelnya RunCloud terasa lebih nyaman karena lebih cepat, dan lebih ramah.

### 2\. Lebih Banyak Pilihan Cloud Provider Hosting

RunCloud memudahkan Anda untuk menghubungkan lebih banyak pilihan cloud provider hosting, mulai dari Linode, Digital Ocean, Vultr, Google Cloud, dan lain-lain. Bahkan Anda bisa menginstall custom VPS dari provider tertentu.

Jika Anda pemula, Anda bisa memilih provider seperti Digital Ocean, Linode, Vultr dan UpCloud karena lebih mudah untuk dihubungkan ke RunCloud.

![Pilihan provider cloud di RunCloud](https://img.penasihathosting.com/2025/May/Pilihan-provider-cloud-di-RunCloud.webp)

### 3\. Performance yang Mengesankan

Saya melakukan pengujian dengan dua konfigurasi yang berbeda untuk melihat mana yang memiliki performance lebih cepat antara:

- RunCloud Native Nginx + RunCloud HUB dengan FastCGI Caching

- RunCloud OpenLiteSpeed + LiteSpeed Caching

Mengapa saya tidak melakukan pengujian dengan Nginx + Apache2 Hybrid? Karena dipastikan lebih lambat dari Native Nginx ataupun OpenLiteSpeed.

Dan mengapa tidak menguji metode caching menggunakan Redis? Saya sudah membandingkan nya sebelumnya. Hasilnya metode caching menggunakan FastCGI lebih cepat.

Jadi, untuk menentukan mana yang lebih cepat, saya monitoring TTFB dari keduanya.

Monitoring TTFB menggunakan GTMetrix selama hampir 3 hari yang dimulai dari tanggal 30 Juli 2021 hingga 1 Agustus 2021 dan lokasi server yang digunakan adalah di Singapore.

Pertama, yang menggunakan konfigurasi Native Nginx + RunCloud HUB dengan FastCGI Caching didapatkan TTFB sebagai berikut:

**Min:** 34ms**Max:** 217ms**Average:** 46,94 ms

![RunCloud AMD Native Nginx + FastCGI](https://img.penasihathosting.com/2025/May/RunCloud-AMD-Nginx.webp)

Grafik TTFB konfigurasi Native Nginx + RunCloud HUB FasctCGI Caching

Sedangkan yang OpenLiteSpeed + LiteSpeed Caching:

**Min:** 27ms**Max:** 642ms**Average:** 43,89 ms

![RunCloud OpenLiteSpeed](https://img.penasihathosting.com/2025/May/RunCloud-OpenLiteSpeed.webp)

Grafik TTFB konfigurasi OpenLIteSpeed + LiteSpeed Caching

**Perbandingannya:**

\[table id=80 /\]

**Kesimpulannya:**

Ternyata konfigurasi OpenLiteSpeed + LiteSpeed Caching lebih cepat dari Native Nginx + FastCGI Caching. Selain lebih cepat juga terlihat lebih stabil, meskipun ditemukan satu kali secara tiba-tiba TTFB nya meningkat sangat tinggi (max) di 642 ms, tetapi secara keseluruhan lebih stabil.

Pertanyaannya, apakah rata-rata 46,94ms dan 43,89ms tergolong cepat? Tentu saja cepat, TTFB dibawah 50ms tergolong cepat.

**Studi kasus:**

Sebagai gambaran dan perbandingan, saya memindahkan salah satu website saya dari Kinsta paket Business 1 (seharga $100/bulan) ke RunCloud dengan menggunakan provider Digital Ocean. Konfigurasi yang saya pakai sbb:

- Digital Ocean 2 GB RAM / 1 AMD CPU (Premium AMD)

- RunCloud dengan Native Nginx + FastCGI Caching

Hasilnya, berdasarkan data monitoring load time by Pingdom, load time website saya menurun (artinya lebih cepat), seperti terlihat dalam grafik dibawah ini:

![Setelah pindah ke RunCloud](https://img.penasihathosting.com/2025/May/Setelah-pindah-ke-RunCloud-1.webp)

Setelah saya optimasi, load time website saya bahkan menjadi 50% lebih cepat dari sebelumnya (before= 613 / after= 318ms)

![Setelah dioptimasi](https://img.penasihathosting.com/2025/May/Setelah-dioptimasi.webp)

Bagaimana jika dibandingkan dengan pesaingnya, seperti SpinupWP, GridPane dan CloudWays?

Berdasarkan pengujian saya (mulai dari yang paling cepat) **SpinupWP > CloudWays >  RunCloud.**

**Mungkin Anda bertanya-tanya:**

**Apakah RunCloud telah dioptimalkan sedemikian rupa untuk menunjang performance website WordPress?**

Ternyata tidak atau sayangnya tidak.

RunCloud mirip dengan CloudWays, mereka tidak mengkhususkan dirinya sebagai cloud panel khusus WordPress. Faktanya, Anda bisa instal aplikasi selain WordPress.

Jika Anda mencari cloud panel yang memang dioptimalkan untuk WordPress, SpinupWP dan GridPane mungkin lebih tepat untuk Anda. Konfigurasi yang mereka buat telah dioptimalkan sedemikian rupa untuk website WordPress, jadi tidak heran speed nya lebih cepat dari RunCloud.

### 4\. Memiliki Fitur-fitur yang Kaya

Salah satu poin yang saya sukai dari RunCloud dibandingkan cloud panel yang lain adalah mereka memiliki fitur-fitur yang kaya.

Semua yang Anda butuhkan, mulai dari:

**1\. Dua pilihan konfigurasi server antara Nginx dan OpenLiteSpeed**

![konfigurasi server](https://img.penasihathosting.com/2025/May/konfigurasi-server.webp)

**2\. Jika Anda memilih Nginx, maka terdapat tiga pilihan stack yang bisa Anda pilih sesuai kebutuhan (Native Nginx, Nginx + Apache2 Hybrid, Nginx + Custom Config).**

Pilihan Native Nginx lebih cepat, tetapi Anda tidak bisa menggunakan .htaccess.

![web application stack](https://img.penasihathosting.com/2025/May/web-application-stack-1.webp)

**3\. Server monitoring (untuk track penggunaan sumber daya) dan website monitoring (untuk track traffic)**

![Track penggunaan resources](https://img.penasihathosting.com/2025/May/Track-penggunaan-resources.webp)

**4\. Fitur untuk memudahkan pengguna WordPress, seperti:**

- Install WordPress dengan 1x klik, mirip fitur install WordPress di Softaculous di cPanel

![Install WordPress 1x klik](https://img.penasihathosting.com/2025/May/Install-WordPress-1x-klik.webp)

- WordPress staging (bisa custom push - per database, ini salah satu alasan lainnya yang membuat saya memilih RunCloud.) Dan proses push ataupun sync baik dari production site ke staging atau sebaliknya, berdasarkan pengujian saya, terbilang sangat cepat.

![Fitur staging](https://img.penasihathosting.com/2025/May/Fitur-staging-.webp)

- RunCloud juga menyediakan free URL staging, sehingga Anda bisa membuat staging website tanpa perlu mengatur sub-domain di DNS terlebih dahulu. Jadi, lebih memudahkan dan lebih menghemat waktu.P/S: Perhatikan pengaturan "search engine visibility" ketika Anda melakukan _push_ atau _sync_ dari staging ke production. Karena jika sebelumnya Anda memilih untuk memblock search engine untuk mengindex website Anda, maka di website production Anda, pilihan ini tidak akan berubah atau tetap tercentang.

![Perhatikan search engine visibility](https://img.penasihathosting.com/2025/May/Perhatikan-search-engine-visibility.webp)

- RunCloud HUB (plugin caching buatan RunCloud dan bisa memilih metode caching nya, antara FastCGI atau Redis - berdasarkan pengujian saya, FastCGI lebih cepat). P/S: Anda hanya bisa menggunakan RunCloud HUB jika memilih konfigurasi server Nginx.

![RunCloud HUB](https://img.penasihathosting.com/2025/May/RunCloud-HUB.webp)

**5\. File Manager (fitur yang tidak akan Anda dapatkan di cloud panel lainnya.)**

Sayangnya Anda tidak bisa upload file lewat File Manager RunCloud.

![Web Application RunCloud](https://img.penasihathosting.com/2025/May/Web-Application-RunCloud.webp)

**6\. Install SSL dengan mudah, 1x klik di RunCloud**

![Instal SSL dengan mudah di RunCloud](https://img.penasihathosting.com/2025/May/Instal-SSL-dengan-mudah-di-RunCloud.webp)

**Fitur-fitur lainnya:**

- Banyak pilihan integrasi dengan GIT (GitHub, BitBucket,GitLab, dan lain-lain)

- Activity log (yang akan merekam semua aktifitas Anda)

- Clone web application (dimana Anda bisa meng-copy aplikasi Anda (misalnya WordPress) dan memindahkannya ke server yang lain)

- Dan masih banyak lagi..

### 5\. Harga yang _Fair_ dan Lebih Murah Untuk Pengelolaan Banyak Website

Harga di RunCloud bisa terasa mahal atau terasa murah, tergantung dari mana Anda datang.

Jika Anda datang dari shared hosting, maka harga RunCloud terkesan mahal, karena untuk menghosting-kan misalnya satu website saja, Anda harus membayar:

- RunCloud seharga mulai dari $6,67/bulan (tahunan) atau $8/bulan (bulanan)

- dan biaya sewa cloud VPS hosting dari provider yang Anda pilih, misalnya Digital Ocean $5

Totalnya, paling rendah untuk menghosting-kan satu website adalah $13/bulan (dengan asumsi Anda akan memilih pembayaran secara bulanan).

Tetapi, ingat bahwa performance yang akan Anda dapatkan akan jauh berbeda dengan apa yang Anda dapat di [shared hosting](https://penasihathosting.com/direktori/kategori/shared-hosting/). Rasanya seperti ada di dunia yang berbeda.

Saya yakin tanpa perlu membandingkan nya dengan alat seperti GtMetrix pun Anda akan bisa merasakan perbedaan speed nya.

![Harga RunCloud](https://img.penasihathosting.com/2025/May/Harga-RunCloud.webp)

Dibandingkan pesaingnya, seperti SpinupWP dan GridPane, harga RunCloud lebih murah, apapaun kondisinya, mau Anda menghosting-kan satu website atau banyak website sekalipun.

- GridPane: Mulai dari $200 (_harga terbaru!_)

- SpinupWP: $12 untuk 1 server atau $39 untuk 3 server

Tetapi, jika dibandingkan CloudWays:

- RunCloud hanya lebih mahal jika Anda mengelola satu website saja atau memilih paket Basic

- RunCloud akan lebih murah dari CloudWays jika Anda mengelola banyak website.

Saya akan bahas perbedaan harganya dengan CloudWays lebih detail pada artikel yang lain.

### 6\. Gratis Trial 5 Hari + Gratis Migrasi Hosting

Ngomong-ngomong, RunCloud menawarkan trial selama 5 hari, jadi Anda bisa melakukan 'test drive' terlebih dahulu. Mencoba semua fitur RunCloud dan kalau bisa, migrasikan website Anda ke RunCloud dan bandingkan dengan hosting Anda sebelumnya.

Dan ingat juga, bahwa Digital Ocean menawarkan credit $100 selama 60 hari untuk pelanggan baru. Artinya, Anda dapat sewa [VPS](https://penasihathosting.com/direktori/kategori/unmanaged-vps/) gratis di Digital Ocean selama 2 bulan + 5 hari trial di RunCloud.

Penawaran yang sangat menarik, bukan? Dan sangat tidak ada salahnya untuk dicoba, benar?

Jika 5 hari trial dirasa kurang, Anda bisa meminta untuk diperpanjang masa trial nya. Saya sudah mencobanya dan mereka akan dengan senang hati memperpanjang masa trial Anda.

![trial extent](https://img.penasihathosting.com/2025/May/trial-extent.webp)

Ohya, RunCloud juga memberikan [gratis migrasi hosting](https://runcloud.io/migration), sayangnya hanya gratis untuk migrasi satu website saja.

### 7\. Menerima Pembayaran Via PayPal

Saat ini, RunCloud memang hanya menerima pembayaran dengan Visa atau MasterCard saja, tetapi Anda bisa menggunakan PayPal untuk top-up saldo yang bisa digunakan untuk membayar layanan RunCloud.

Selama Anda memiliki saldo atau credit, Anda bisa meneruskan berlangganan Anda di RunCloud tanpa masalah.

## Kekurangan Menggunakan RunCloud

RunCloud adalah pilihan cloud panel yang bagus, salah satu yang terbaik dan favorit saya.

Tapi, saya tidak bisa merekomendasikannya untuk semua orang.

### 1\. Anda Harus Memiliki Dua Account

RunCloud bukanlah provider hosting, saya sudah jelaskan diawal, bukan? :)

Istilah gampangnya, mereka hanya menyediakan [jasa maintenance server](https://harunstudio.com/jasa-maintenance-website/) saja. Adapun server atau hostingnya, Anda sewa di tempat lain.

Itu artinya, Anda memerlukan dua account.

Satu account di RunCloud,dan satu account di provider cloud VPS yang Anda pilih.

Jadi, untuk urusan bayar-membayar juga terpisah. Anda bayar hosting Anda di provider hosting, dan Anda bayar layanan maintenance server Anda di RunCloud.

Memiliki dua account adalah kekurangan, terutama untuk pemula. Anda harus daftar dulu di provider hosting yang Anda pilih, harus verifikasi dulu, menunggu proses verifikasi yang mungkin memakan waktu 1 atau 2 hari.

Tetapi, memang begitulah cara mainnya. Di semua cloud panel, tidak hanya RunCloud, Anda diharuskan membuat account yang terpisah.

Jika dirasa berat, mungkin Anda bisa mempertimbangkan menggunakan CloudWays, karena di CloudWays Anda hanya butuh 1 account saja.

### 2\. Memiliki Keterbatasan-keterbatasan Dalam Hal Support nya

Okay. Sebenarnya normal bahwa provider cloud panel seperti RunCloud memiliki keterbatasan dalam hal support nya.

Faktanya, mereka bukan provider hosting, jadi Anda tidak bisa mengharapkan bantuan support yang sama dengan yang Anda dapatkan di provider shared hosting atau managed VPS hosting Anda sebelumnya.

Mereka akan membantu jika yang Anda tanyakan seputar control panel mereka, tapi ketika Anda misalnya bertanya tentang _error_ yang terjadi pada website WordPress Anda, mungkin mereka tidak bisa membantu karena bukan termasuk cakupan dari support mereka.

Atau paling jauh mungkin mereka mau memandu cara menyelesaikan masalah Anda, tetapi Anda yang harus mengotak-ngatik nya sendiri.

Misalnya, saya mencoba bertanya tentang bagaimana cara membuat custom redirection antara domain saya yang satu ke domain saya yang lain.

![test support RunCloud](https://img.penasihathosting.com/2025/May/test-support-RunCloud.webp)

Sebenarnya, ini mudah dilakukan (bagi yang mengerti tentu saja.)

Mereka bisa membantu membuatkan _rule_ diatas supaya domain saya bisa ke redirect ke domain saya yang lain, tapi mereka tidak bisa log in ke account saya dan membuatkan rule nya di Nginx config, karena memang tidak termasuk cakupan support mereka.

Kemudian, dari pengujian saya, support mereka semua ramah, meski saya merasa bahwa kualitas staf suppot nya tidak semua sama. Dan waktu response support nya terkadang lama (lebih dari 30 menit), terkadang cepat. Tergantung dari kasus yang Anda tanyakan.

Favorit saya dari staf support mereka dalah Rahul Krishnan.

![test support RunCloud](https://img.penasihathosting.com/2025/May/test-support-RunCloud.webp)

Dia mengatakan bahwa mereka bisa membantu error seperti "Establishing database connection" pada website WordPress dan banyak dari klien mereka yang mengelola server dan aplikasi di RunCloud tanpa ada masalah (hassle-free).

Dia benar, setelah hampir 3 minggu menggunakan RunCloud, saya bisa bilang bahwa semua nya berjalan dengan baik, tidak ada bugs (yang saya temukan). Saya bisa menjalankan tugas rutin saya tanpa perlu bantuan teknis. Buat pengguna yang baru saja pindah dari Managed WordPress Hosting ke RunCloud, saya tidak menemukan masalah berarti.

Akan tetapi (sekali lagi) saya tidak bisa merekomendasikan RunCloud ke semua orang.

### 3\. Fitur Backupnya Gratis, Akan Tetapi Memiliki Keterbatasan-Keterbatasan

Saya tidak mengatakan bahwa RunCloud memiliki sistem backup yang jelek dan mesti dimasukkan dalam poin kekurangan, tapi di RunCloud Anda harus memilih antara backup yang gratis atau yang bayar (premium).

Backup gratisnya memungkinkan Anda untuk melakukan backup secara harian, tiga harian dan mingguan dan Anda akan mendapatkan gratis hingga 50 GB.

Bagi banyak pengguna, saya rasa backup free nya sudah cukup bagus. Tapi sayangnya Anda tidak mendapatkan fitur-fitur penting seperti:

- **On-deman backup:** Dimana Anda bisa melakukan backup secara manual

- **Download backup:** Mendownload backup dan menyimpannya di komputer Anda

![Backup pro RunCloud](https://img.penasihathosting.com/2025/May/Backup-pro-RunCloud.webp)

Untuk mendapatkan fitur backup yang lengkap seperti yang dijelaskan dalam gambar diatas, Anda perlu memilih backup pro dengan biaya $1/website/bulan.

Jika Anda hanya menghosting-kan satu atau dua website saja, mungkin kekurangan ini tidak akan menjadi masalah, tapi bagaimana jika Anda mengelola banyak website, katakanlah 10, 20 atau mungkin 30 website?

![Backup RunCloud](https://img.penasihathosting.com/2025/May/Backup-RunCloud.webp)

Yang paling saya sukai dari fitur backup pro nya RunCloud adalah Anda bahkan bisa mengatur frekuensi backup setiap 30 menit sekali dan retensi backupnya hingga 1 bulan.

Dibandingkan pesaing-pesaingnya, seperti CloudWays dan SpinupWP, fitur backup RunCloud ini adalah yang paling lengkap. Saya pikir biaya $1/website/bulan worth it _banget kok!_

Satu-satunya kekurangan adalah tidak bisa memberikan nama pada file backup nya!

Saya biasa memberikan nama pada file backup, misalnya saya namakan "Backup before updating WordPress to version 5.8", tapi sayangnya di RunCloud tidak bisa, jadi saya harus mengingat backup dari tanggal backupnya saja.

![Sistem Backup di RunCloud](https://img.penasihathosting.com/2025/May/Sistem-Backup-di-RunCloud.webp)

## Kesimpulan: Apakah RunCloud Cocok Untuk Anda?

Sekarang, pertanyaan pentingnya adalah - Apakah RunCloud adalah pilihan yang tepat untuk Anda?

Jawabannya tergantung.

Seperti yang saya jelaskan dalam poin support diatas, karena keterbatasan-keterbatasan dalam cakupan support di RunCloud, maka saya tidak bisa menyarankannya untuk semua orang.

Tetapi, sebagai pengguna yang sebelumnya menggunakan Premium Managed WordPress Hosting (Kinsta), saya _happy_ menggunakan RunCloud. Sejauh ini:

- Saya belum menemukan masalah apapun, semuanya berjalan baik, dan tidak ada bugs (yang saya temukan),

- Berdasarkan data monitoring saya, _performance_ nya lebih baik (dengan VPS Digital Ocean seharga $11/bulan + RunCloud dengan Native Nginx + RunCloud HUB + FastCGI Caching)

- Harga yang murah

- Dan fitur-fitur yang dapat memudahkan tugas harian saya sebagai blogger, seperti WordPress Staging, SSL dan File Manager. Saya bahkan jarang sekali menyentuh CLI atau bahkan FTP.

Tetapi, saya tidak bisa mengatakan bahwa saya bisa berlepas diri tanpa memikirkan hosting sama sekali. Memang tidak sepraktis menggunakan Managed WordPress Hosting seperti Kinsta, tapi walaupun Anda punya pengetahuan teknis, Anda tidak akan terlalu sering bermain-main dengan hosting ketika menggunakan RunCloud.

Agar memudahkan Anda, saya sudah merinci beberapa kondisi yang mungkin bisa membantu Anda dalam mengambil keputusan:

Jika Anda seorang developer, RunCloud sangat cocok untuk Anda.

Jika Anda seorang Google _Expert_ (terbiasa mencari masalah Anda sendiri atau minimal bisa mengikuti panduan yang diberikan oleh staf RunCloud terhadap masalah teknis pada website Anda), RunCloud cocok untuk Anda.

Jika Anda mengerti menggunakan control panel cPanel dengan baik dan mau belajar menggunakan cloud panel nya RunCloud, maka RunCloud juga cocok untuk Anda.

Jika Anda _agency_, memiliki banyak klien, dan mencari pilihan cloud panel yang murah dengan banyak fitur yang lebih dapat memudahkan tugas harian Anda (artinya lebih produktif), maka RunCloud cocok untuk Anda.

Jika Anda datang dari Managed WordPress Hosting, seperti [Kinsta](https://penasihathosting.com/review-kinsta/), WP Engine, WPX dan semacamnya, menginginkan harga yang lebih murah, tetapi dengan performance yang tidak kalah, maka RunCloud dengan OpenLiteSpeed + LiteSpeed Caching atau RunCloud dengan Native Nginx + RunCloud HUB + FastCGI Caching adalah konfigurasi yang tepat untuk Anda.

Tetapi, jika Anda total-newbie, baru datang dari shared hosting, maka RunCloud tidak cocok untuk Anda, kecuali Anda mau menginvestasikan sebagian dari waktu Anda untuk belajar.

Dan jika Anda hanya ingin menghosting-kan satu website saja, dan tidak begitu membutuhkan fitur-fitur yang banyak yang ada di cloud panel nya RunCloud, maka RunCloud juga tidak cocok untuk Anda. Cloud panel seperti SpinupWP mungkin akan lebih tepat untuk Anda.

Begitu kira-kira..

Jadi, jika Anda menyukai pendekatan yang dilakukan RunCloud, silahkan coba RunCloud. Ingat bahwa [RunCloud](https://runcloud.io) menawarkan trial selama 5 hari (dan bisa di _extend_ jika Anda memintanya) + [VPS gratis dari Digital Ocean](/go/digitalocean) (affiliate link) selama 60 hari (credit $100).

Atau jika Anda ingin yang mirip dengan RunCloud, tapi mau yang lebih sederhana dan lebih cocok untuk pemula, pertimbangkan [CloudWays](/review-cloudways/).

## Cara Kerja RunCloud

Untuk membantu Anda memahami bagaimana cara kerja RunCloud, saya akan menunjukan kepada Anda [cara menginstall website WordPress](/cara-membuat-website/) di RunCloud.

Ada dua tahapan:

- [Pertama, hubungkan server Cloud VPS hosting Anda ke RunCloud](#menghubungkan)

- [Kedua, install WordPress (1x click)](#kedua)

_P/S: Saya mungkin akan menulis tutorial cara migrasi hosting ke RunCloud pada kesempatan yang lain._

### **Menghubungkan server ke RunCloud**

**1\. Buat account atau daftar di provider cloud VPS hosting.**

Favorit saya [Digital Ocean](/go/digitalocean) /affiliate link. Anda akan mendapatkan credit $100 yang bisa digunakan dalam 60 hari (hanya untuk pengguna baru).

**2\. Buat atau generate token API yang akan digunakan untuk setup server di RunClod.**

Klik menu API yang terletak di bagian sidebar sebelah kiri bawah layar Anda dan klik tombol "Generate New Token."

![Generate token](https://img.penasihathosting.com/2025/May/Generate-token.webp)

Kemudian, copy token yang baru Anda buat.

_Token ini akan digunakan untuk menghubungkan RunCloud dengan Digital Ocean._

![copy token](https://img.penasihathosting.com/2025/May/copy-token.webp)

**3\. Kembali ke dashboard RunCloud Anda, klik pada tombol "Let's get Started" atau klik tombol "Connect a New Server"**

Ikuti langkah-langkah dibawah ini dengan catatan:

- Di langkah 3, Anda bisa memilih OpenLiteSpeed (berdasarkan pengujian saya, performance nya lebih cepat dan lebih stabil.)

- Di langkah 4, masukkan kode token yang telah Anda buat pada langkah sebelumnya.

![](https://img.penasihathosting.com/2025/May/Connect-Server-Digital-Ocean-ke-RunCloud.webp "Connect-Server-Digital-Ocean-ke-RunCloud")

**4\. Build Server Digital Ocean Anda dengan pengaturan sebagai berikut:**

1. Pilih OS Ubuntu versi terbaru, saat ini, versi terbaru adalah 20.04 (LTS) x64

3. Pilih paket, jika Anda baru memulai dan punya budget lebih, saya menyarankan untuk menggunakan Basic (Premium AMD) karena performa nya lebih cepat dari yang Regular.

5. Lokasi server nya pilih yang terdekat dengan target pengunjung website Anda. Jika target nya Indonesia, maka pilih Singapore 1.

7. Pilih spesifikasi server Anda. Jika Anda baru memulai, spesifikasi hosting dengan 1 GB RAM dan 1 CPU sudah lebih dari cukup.

9. Tulis nama server (misalnya ServerSG)

11. Ceklis

13. Klik tombol "Add this server"

![Build Server Digital Ocean di RunCloud](https://img.penasihathosting.com/2025/May/Build-Server-Digital-Ocean-di-RunCloud.webp)

**5\. RunCloud akan mengkonfigurasi server Anda. Proses ini memakan waktu 5 - 10 menit.**

Jika sudah selesai, Anda akan melihat dashboard server Anda seperti ini:

![Dashboard server RunCloud](https://img.penasihathosting.com/2025/May/Dashboard-server-RunCloud.webp)

### Instal aplikasi WordPress ke dalam server Anda.

**1\. Klik menu "Web Application" yang terletak di sisi kiri atas layar Anda.**

Kemudian, klik tombol hijau "Create Web App".

![Create Web App (RunCloud)](https://img.penasihathosting.com/2025/May/Create-Web-App-RunCloud.webp)

dan pilih 1 Click WordPress.

![Pilih WordPress (RunCloud)](https://img.penasihathosting.com/2025/May/Pilih-WordPress-RunCloud.webp)

**2\. Terakhir, ikuti langkah-langkah berikut:**

1. Tulis nama aplikasi Anda, misal: penasihat-hosting

3. Pilih "Use my own domain/sub domain"

5. Masukkan nama domain: misal: penasihathosting.com

7. Ceklis "Enable www version..." jika Anda menggunakan dua versi. Jangan lupa pilih versi domain yang Anda inginkan, jika ingin menggunakan "www.", maka pilih "www."

9. Setup DNS records domain Anda di provider tempat Anda beli domain dengan menambahkan A Record.

11. Biarkan dalam kondisi dicentang, kecuali Anda ingin membuat system user baru

13. Biarkan apa adanya, kecuali Anda sudah membuat database sebelumnya (optional)

15. Klik tombol "Add web application..."

![Menambahkan WordPress ke Server](https://img.penasihathosting.com/2025/May/Menambahkan-WordPress-ke-Server.webp)

**3\. WordPress Anda telah terinstall, Anda bisa mengkonfirmasi nya langsung dengan mengunjungi alamat domain Anda.**

Ingat, untuk mengakses web app yang baru Anda install, buka atau klik menu "Web Application" dan klik nama domainnya.

![aplikasi WordPress Anda](https://img.penasihathosting.com/2025/May/aplikasi-WordPress-Anda.webp)

Sekian.

Punya pertanyaan terkait RunCloud? Silahkan kirimkan melalui kolom komentar yang ada dibawah. Biasanya pertanyaan yang masuk akan saya balas kurang dari 48 jam.
