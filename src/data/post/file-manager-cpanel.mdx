---
title: "Mengelola File Website di cPanel: File Manager dan FTP"
publishDate: 2016-12-13
updateDate: 2025-05-21
categories: 
  - "Panduan Lengkap"
tags: 
  - "panduan-cpanel"
  - "cpanel"
  - "file-manager"
  - "ftp"
  - "hosting"
image: https://img.penasihathosting.com/2025/cpanel/file-manager-cpanel.webp
excerpt: "Cara menggunakan File Manager dan FTP untuk mengelola file website di cPanel. Panduan lengkap untuk mengupload, mengedit, dan mengorganisir file website Anda."
metadata:
  title: "Mengelola File Website di cPanel: File Manager dan FTP (Edisi 2025)"
  description: "Pelajari cara menggunakan File Manager dan FTP untuk mengelola file website di cPanel, termasuk cara mengupload, mengedit, dan mengorganisir file dengan informasi terbaru tahun 2025."
  guideId: "cpanel-guide"
  chapterIndex: 6
  chapterTitle: "Mengelola File Website"
---

# Mengelola File Website di cPanel: File Manager dan FTP

Setelah Anda memahami cara [mengatur email account di cPanel](/email-cpanel/), langkah selanjutnya adalah mempelajari cara mengelola file website. Ini adalah salah satu fungsi paling penting di cPanel karena memungkinkan Anda untuk mengupload, mengedit, dan mengorganisir file website Anda.

## Pengenalan Pengelolaan File di cPanel

Bagian "Files" pada cPanel memungkinkan Anda untuk mengupload file, membackup website, mengelola File Transfer Protocol (FTP) dan juga melihat pemakaian disk.

Ngomong-ngomong, FTP adalah cara lain untuk mengelola file website Anda, yang mana dengan FTP Anda juga dapat mengupload file-file ke hosting Anda.

Jadi, saya akan menjelaskan tentang mengelola files ini dengan dua cara, yaitu menggunakan "File Manager" dan "FTP".

Di bagian Files, Anda akan menemukan beberapa fitur utama:

1. **File Manager** - Untuk mengelola file langsung dari browser
2. **Disk Usage** - Untuk memeriksa penggunaan disk space
3. **FTP Accounts** - Untuk membuat dan mengelola akun FTP
4. **Backup** - Untuk membackup dan restore file website
5. **Images** - Untuk mengelola dan mengoptimalkan gambar
6. **Web Disk** - Untuk mengakses file hosting sebagai drive virtual

Mari kita bahas dua metode utama untuk mengelola file: File Manager dan FTP.

## Menggunakan File Manager

File Manager pada cPanel memungkinkan Anda untuk mengelola seluruh file website tanpa perlu menggunakan aplikasi FTP atau aplikasi lainnya yang perlu Anda download terlebih dahulu pada komputer Anda.

Pertama-tama, klik "File Manager", kemudian Anda akan diarahkan kehalaman dibawah ini:

![Bagian Files di cPanel](https://img.penasihathosting.com/2025/cpanel/File-Manager-1024x621.webp)

Disinilah tempat dimana seluruh file website Anda disimpan, jadi pastikan Anda berhati-hati ketika membuat suatu perubahan.

### Root Folder dan Sub-Folder

Pada dasarnya, File Manager ini tidak jauh berbeda dengan file manager pada komputer.

Pertama, disebelah kiri Anda dapat menemukan root folder (/home/<USER>

![Struktur root folder dan sub-folder di File Manager cPanel](https://penasihathosting.com/wp-content/uploads/2016/12/root-folder.webp "root folder")

### Selected Folder / Folder yang Dipilih

Kemudian pada sisi sebelah kanan, Anda dapat melihat konten-konten, seperti folder dan text, yang mana akan terlihat ketika Anda memilih/mengklik sub-folder pada sisi sebelah kiri.

![Bagian kanan File Manager menampilkan isi folder](https://img.penasihathosting.com/2025/cpanel/Bagian-kanan-dari-file-manager-1024x621.webp)

Mengetahui hal-hal diatas, (sekali lagi) sama halnya dengan file manager pada komputer, Anda juga dapat membuat folder baru dan mengupload file.

### Membuat Folder Baru

Folder membantu Anda membuat file-file website Anda terorganisir dan membuat struktur website Anda mudah dimengerti.

Untuk membuat folder baru, klik tombol "+ Folder" disebelah atas kiri tampilan "File Manager" Anda, seperti terlihat dibawah ini:

![Tombol untuk membuat folder baru di File Manager cPanel](https://img.penasihathosting.com/2025/cpanel/Membuat-folder-baru-1024x487.webp)

Kemudian akan muncul pop-up seperti dibawah ini dan Anda akan diminta untuk memasukkan nama folder dan juga dimana folder tersebut Anda letakkan.

![Pop-up untuk memasukkan nama dan lokasi folder baru di File Manager cPanel](https://img.penasihathosting.com/2025/cpanel/Membuat-nama-folder-1024x475.webp)

Setelah selesai, klik "Create New Folder".

### Mengupload File Menggunakan File Manager

Untuk menambahkan sebuah file - sebagai contoh, file Word/PDF yang Anda ingin orang dapat mendownloadnya dari website Anda - pertama-tama Anda perlu menentukan dimana akan Anda letakkan file tersebut.

Sebagai contoh, digambar dibawah ini saya memilih "etc" (1), lalu klik "Upload":

![Tombol upload file di File Manager cPanel](https://img.penasihathosting.com/2025/cpanel/Upload-file-1024x767.webp)

Terakhir Anda akan diminta untuk memilih file yang akan Anda upload atau drag saja filenya ke halaman upload seperti yang terlihat dibawah ini:

![Antarmuka untuk mengupload file dengan drag and drop di File Manager cPanel](https://penasihathosting.com/wp-content/uploads/2022/07/34.-cpanel-Mengupload-File2.webp)

### Fitur-fitur Lain di File Manager

Selain membuat folder dan mengupload file, File Manager juga menyediakan berbagai fitur lain:

1. **Edit File** - Mengedit file teks langsung di browser
2. **View** - Melihat isi file tanpa mengeditnya
3. **Copy/Move** - Menyalin atau memindahkan file dan folder
4. **Rename** - Mengganti nama file dan folder
5. **Delete** - Menghapus file dan folder
6. **Permissions** - Mengatur izin akses file (chmod)
7. **Extract** - Mengekstrak file arsip seperti ZIP, TAR, GZ
8. **Compress** - Membuat file arsip dari file dan folder

### Praktik Terbaik Menggunakan File Manager

Berikut beberapa praktik terbaik saat menggunakan File Manager:

1. **Selalu backup sebelum mengedit** - Buat salinan file sebelum mengeditnya
2. **Perhatikan izin file** - Atur izin file dengan benar (biasanya 644 untuk file, 755 untuk folder)
3. **Gunakan fitur pencarian** - Manfaatkan fitur pencarian untuk menemukan file dengan cepat
4. **Jangan hapus file sistem** - Berhati-hatilah saat menghapus file, terutama di folder sistem
5. **Gunakan editor yang sesuai** - Pilih editor yang sesuai dengan jenis file yang diedit

## Menggunakan FTP Account 

Sebenarnya, jika Anda sudah membuat account cPanel, Anda seharusnya dapat langsung menggunakan username dan password cPanel Anda untuk log in ke FTP Anda.

Namun, bagaimana bila Anda ingin memberikan seseorang izin untuk dapat mengupload file menggunakan FTP client (seperti [FileZilla](https://filezilla-project.org/) sebagai contoh).

Tentu Anda tidak ingin memberikan username dan password cPanel Anda bukan?

Karena itu Anda membutuhkan username dan password baru yang akan digunakan oleh orang lain selain Anda agar dapat mengkases FTP Anda.

Pertama-tama, klik "FTP Accounts" pada cPanel Anda.

![Bagian FTP Accounts di cPanel](https://img.penasihathosting.com/2025/cpanel/FTP-Accounts-1024x626.webp)

Dan Anda akan diarahkan kehalaman "FTP Accounts" seperti dibawah ini:

![Halaman membuat akun FTP baru di cPanel](https://img.penasihathosting.com/2025/cpanel/Membuat-akun-FTP-899x1024.webp)

**Kemudian ikuti langkah-langkah berikut:**

1. **Login:** Nama log in ini akan digunakan oleh pengguna lain, jadi pastikan Anda membuat nama yang mudah diingat.

3. **Password:** pastikan Anda selalu membuat password yang aman, gunakan angka, kombinasi huruf besar dan kecil, letters, dll atau agar mudah gunakan password generator dan jangan lupa untuk menyimpannya.

5. **Directory:** Kolom "Directory" disini maksudnya adalah akses tingkat tertinggi FTP Account baru yang Anda buat, katakanlah Anda memasukkan public\_html pada kolom "directory", itu artinya FTP Account yang Anda buat memiliki akses ke directory /home/<USER>/public\_html hingga ke sub-directoriesnya.

7. **Quota:** Terakhir, Anda perlu menetapkan berapa quota atau space yang diperlukan untuk FTP account yang baru Anda buat. Quota ini bisa limited/terbatas dan bisa juga unlimited.

### Mengelola FTP Accounts

Sekarang Anda dapat mengelola seluruh FTP accounts Anda pada bagian dibawah ini, seperti mengganti password, mengganti quota, menghapus FTP account dan mengatur FTP client.

![Daftar dan opsi pengelolaan FTP Accounts di cPanel](https://img.penasihathosting.com/2025/cpanel/Informasi-akun-FTP-1024x278.webp)

### Menggunakan FTP Client

Untuk menggunakan FTP, Anda memerlukan FTP client seperti FileZilla. Berikut langkah-langkah menggunakan FileZilla:

1. **Download dan instal FileZilla** dari [situs resmi](https://filezilla-project.org/)
2. **Buka FileZilla** dan klik "File > Site Manager"
3. **Klik "New Site"** dan beri nama
4. **Masukkan informasi koneksi**:
   - Host: ftp.domainanda.com (atau IP server)
   - Port: 21
   - Protocol: FTP - File Transfer Protocol
   - Encryption: Use explicit FTP over TLS if available
   - Logon Type: Normal
   - User: username FTP Anda
   - Password: password FTP Anda
5. **Klik "Connect"** untuk terhubung ke server

Setelah terhubung, Anda akan melihat panel dengan struktur folder:
- Panel kiri menampilkan file lokal di komputer Anda
- Panel kanan menampilkan file di server hosting

Anda dapat menyeret file dari satu panel ke panel lainnya untuk mengupload atau mendownload file.

### Keuntungan Menggunakan FTP

FTP memiliki beberapa keuntungan dibandingkan File Manager:

1. **Transfer file lebih cepat** - Terutama untuk file besar atau banyak file sekaligus
2. **Koneksi yang stabil** - Tidak terpengaruh oleh timeout browser
3. **Drag and drop mudah** - Memudahkan transfer file massal
4. **Sinkronisasi folder** - Beberapa client FTP mendukung sinkronisasi folder
5. **Akses offline** - Beberapa client FTP memungkinkan Anda melihat struktur folder offline

### Praktik Terbaik Menggunakan FTP

Berikut beberapa praktik terbaik saat menggunakan FTP:

1. **Gunakan SFTP atau FTPS** - Lebih aman daripada FTP biasa
2. **Batasi akses directory** - Berikan akses hanya ke folder yang diperlukan
3. **Atur quota** - Tetapkan batas penggunaan disk untuk mencegah penyalahgunaan
4. **Gunakan password yang kuat** - Hindari password yang mudah ditebak
5. **Hapus akun yang tidak digunakan** - Jangan biarkan akun FTP tidak aktif tetap ada

## File Manager vs FTP: Kapan Menggunakan Masing-masing?

Kedua metode memiliki kelebihan dan kekurangan. Berikut panduan kapan menggunakan masing-masing:

### Gunakan File Manager ketika:

- Anda perlu melakukan perubahan kecil dan cepat
- Anda tidak ingin menginstal software tambahan
- Anda mengakses dari komputer yang berbeda-beda
- Anda perlu mengedit file langsung di server
- Anda hanya perlu mengupload beberapa file kecil

### Gunakan FTP ketika:

- Anda perlu mengupload banyak file sekaligus
- Anda bekerja dengan file berukuran besar
- Anda perlu sinkronisasi folder lokal dan server
- Anda bekerja dengan koneksi internet yang tidak stabil
- Anda perlu memberikan akses terbatas kepada orang lain

## Kesimpulan

Mengelola file website adalah keterampilan penting dalam mengelola website Anda. Dengan memahami cara menggunakan File Manager dan FTP, Anda dapat dengan mudah mengupload, mengedit, dan mengorganisir file website Anda.

Pada [bab berikutnya](/backup-cpanel/), kita akan mempelajari cara melakukan backup file website di cPanel, langkah penting untuk melindungi data website Anda.

<div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border-l-4 border-primary my-8">
  <h4 class="text-lg font-bold text-blue-800 dark:text-blue-300 mb-2">Tips Keamanan</h4>
  <p class="text-sm text-gray-700 dark:text-gray-300">
    Selalu atur izin file dengan benar untuk mencegah akses yang tidak sah. File biasa sebaiknya diatur ke 644 (pemilik dapat membaca dan menulis, grup dan publik hanya dapat membaca), dan folder sebaiknya diatur ke 755 (pemilik dapat membaca, menulis, dan mengeksekusi, grup dan publik hanya dapat membaca dan mengeksekusi).
  </p>
</div>
