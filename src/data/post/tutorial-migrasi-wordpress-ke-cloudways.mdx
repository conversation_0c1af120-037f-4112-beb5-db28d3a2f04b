---
title: "Tutorial Migrasi WordPress ke Cloudways"
date: 2023-05-29
category: "Tutorial"
image: "https://img.penasihathosting.com/2025/May/install-wp-di-cloudways.webp"
excerpt: "Panduan lengkap cara migrasi website WordPress ke Cloudways dengan mudah. Pelajari langkah-langkah migrasi menggunakan plugin Cloudways WordPress Migrator dan konfigurasi DNS."
metadata:
  title: "Tutorial Migrasi WordPress ke Cloudways | Panduan Lengkap"
  description: "Panduan lengkap cara migrasi website WordPress ke Cloudways dengan mudah. Pelajari langkah-langkah migrasi menggunakan plugin Cloudways WordPress Migrator dan konfigurasi DNS."
---

import PromoBanner from '~/components/ui/PromoBanner.astro';

Di artikel ini, saya akan membahas tentang bagaimana cara melakukan migrasi WordPress ke Cloudways.

Sebelumnya, saya telah mempublikasikan dua artikel dalam rangkaian tulisan saya tentang Cloudways:

- [Review Cloudways lengkap dengan performance test (perbandingan Digital Ocean standard vs premium)](https://penasihathosting.com/review-cloudways/)

- [Tutorial cara install WordPress di Cloudways](https://penasihathosting.com/install-wordpress-cloudways/)

Cloudways memang menawarkan free migrasi untuk satu website, namun jika Anda ingin melakukan migrasi sendiri, Anda bisa mengikuti tutorial ini.

Tenang saja, caranya cukup mudah.

Jika Anda mengalami kesulitan dalam proses migrasi ini, ingat bahwa Anda bisa selalu menghubungi saya via email di _**randi(at)penasihathosting.com.**_

Biasanya, pesan yang masuk saya balas kurang dari 12 jam, tapi jika saya sedang online, biasanya pesan yang masuk akan langsung saya balas.

### 1\. Buat account (gratis) Cloudways Anda

Pertama, jika Anda belum mempunyai account Cloudways, Anda perlu membuat nya terlebih dahulu.

<PromoBanner
  title="Cloudways Hosting"
  description="Dapatkan diskon 30% selama 2 bulan dengan kode promo di bawah ini. Gratis trial 3 hari tanpa perlu kartu kredit!"
  ctaText="Coba Cloudways Sekarang"
  ctaLink="https://penasihathosting.com/go/cloudways"
  promoCode="PHCW30"
  imageSrc="https://img.penasihathosting.com/2025/May/kode-promo-Cloudways.webp"
  imageAlt="Kode Promo Cloudways untuk diskon 30%"
  disclaimer="Support Anda membantu website ini terus berjalan! Saya menggunakan link affiliasi, dimana saya akan menerima komisi $65 dari pembelian hosting Cloudways Anda. Anda tidak akan dikenakan biaya tambahan."
/>

### 2\. Persiapkan Server dan Aplikasi WordPress Anda

Setelah mendaftar, pertama sekali Anda perlu mengatur server. Klik "Add application".

Oh ya, dibawah ini adalah tampilan user interface Cloudways yang baru. Jika ternyata tampilan user interface Anda masih menggunakan yang classic, jangan lupa untuk menggantinya terlebih dahulu ke yang baru.

![Tampilan dashboard Cloudways dengan tombol Add Application](https://img.penasihathosting.com/2025/May/klik-tombol-add-application-1024x815.webp "Tampilan dashboard Cloudways dengan tombol Add Application")

**ISI DETAIL APLIKASI DAN SERVER**

Anda bisa memilih versi WordPress terbaru.

Anda juga bisa memilih Clean (No Cloudways Optimization) jika Anda ingin mengoptimasi WordPress Anda sendiri (tidak disarankan untuk pemula).

![Pilih aplikasi WordPress di Cloudways](https://img.penasihathosting.com/2025/May/pilih-apliaksi.webp "Pilih aplikasi WordPress di Cloudways")

Kemudian isi detail aplikasi Anda. Anda bebas memilih nama yang Anda sukai. Ini tidak akan mempgengaruhi apapun dan Anda juga bisa menggantinya nanti.

![Form pengisian detail aplikasi WordPress](https://img.penasihathosting.com/2025/May/isi-detail-aplikasi.webp "Form pengisian detail aplikasi WordPress")

**PILIH SERVER, SIZE DAN LOCATION**

Setelah itu, ikuti langkah-langkah berikut:

- Pilih server: jika Anda baru memulai atau tidak yakin, saran saya pilih Digital Ocean (2)
- Piliih server size: tergantung dari spesifikasi website Anda. Jika Anda tidak yakin, Anda bisa mulai dari 1 atau 2 GB terlebih dahulu. Ingat bahwa Anda bisa meng-upgrade server size Anda nantinya dengan mudah. (3)
- Pilih location: jika website Anda berbahasa Indonesia, pilih Singapore. Jika international, pilih salah satu yang ada di United States. (4)
- Perhatikan semua detail server dan aplikasi Anda kembali. Jika sudah klik tombol "LAUNCH NOW" yang terletak di kanan bawah layar Anda. (5)

![Form pemilihan server, ukuran, dan lokasi di Cloudways](https://img.penasihathosting.com/2025/May/Pilih-server-Anda.webp "Form pemilihan server, ukuran, dan lokasi di Cloudways")

Anda perlu menunggu sebentar hingga server Anda selesai dibuat. Estimasi nya sekitar 7 menit

![Proses pembuatan server Cloudways](https://img.penasihathosting.com/2025/May/proses-launch-server-1024x357.webp "Proses pembuatan server Cloudways")

### 2\. Migrasi WordPress Anda ke Cloudways Menggunakan Plugin Cloudways WordPress Migrator

**INSTALL PLUGIN CLOUDWAYS WORDPRESS MIGRATOR**

Selanjutnya, log in ke website WordPress di hosting lama Anda. Instal plugin **Cloudways WordPress Migrator**.

![Halaman instalasi plugin Cloudways WordPress Migrator](https://img.penasihathosting.com/2025/May/install-plugin-cloudways-wp-migrator-1024x434.webp "Halaman instalasi plugin Cloudways WordPress Migrator")

Masukkan email Anda. Email ini tidak perlu sama dengan email admin WordPress Anda. Centang "I agree.." dan klik tombol "Migrate".

![Form migrasi Cloudways dengan field email](https://img.penasihathosting.com/2025/May/migrate-to-cloudways.webp "Form migrasi Cloudways dengan field email")

**ISI DETAIL MIGRASI**

Ada 5 form yang perlu Anda isi seperti yang saya beri nomor pada _screenshoot_ dibawah. Abaikan yang lainnya jika Anda ragu atau tidak yakin.

![Form detail migrasi Cloudways](https://img.penasihathosting.com/2025/May/migrate-to-cloudways-1.webp "Form detail migrasi Cloudways")

Bagaimana cara mengisi detalnya?

**(1) Destination Site URL:** Isi dengan domain sementara yang disediakan Cloudways di Application URL.

![URL domain sementara Cloudways](https://img.penasihathosting.com/2025/May/domain-sementara.webp "URL domain sementara Cloudways")

**(2) SFTP Host/Server Address:** copy public IP

![IP Public server Cloudways](https://img.penasihathosting.com/2025/May/copy-IP-Public.webp "IP Public server Cloudways")

**(3) Database Name:** copy DB name

![Nama database Cloudways](https://img.penasihathosting.com/2025/May/copy-database.webp "Nama database Cloudways")

**(4) SFTP Username dan (5) SFTP Password:** Anda perlu membuat username dan password sendiri.

Pindahkan tab dari "Database" ke "SSH/SFTP" kemudian klik tombol "Add SFTP User" dan buat App Credentials Anda, yaitu username dan password. Nah, username dan password yang Anda buat inilah yang perlu Anda simpan dan isi di form migrasi.

![Form pembuatan kredensial SFTP Cloudways](https://img.penasihathosting.com/2025/May/buat-SFTP.webp "Form pembuatan kredensial SFTP Cloudways")

Jika sudah terisi semua klik tombol hijau "Migrate"

**VALIDASI DAN MULAI PROSES MIGRASI**

Plugin Cloudways WP Migrator akan memulai dengan memvalidasi details yang Anda isi, kemudian proses migrasi akan dilakukan.

![Proses validasi data migrasi Cloudways](https://img.penasihathosting.com/2025/May/Cloudways-akan-mulai-memverifikasi-data-Anda.webp "Proses validasi data migrasi Cloudways")

Tergantung dari seberapa besar ukuran website Anda dan kecepatan internet Anda, tapi harusnya prosesnya tidak akan terlalu memakan waktu lama.

![Proses migrasi Cloudways sedang berlangsung](https://img.penasihathosting.com/2025/May/proses-migrasi-sedang-berlangsung.webp "Proses migrasi Cloudways sedang berlangsung")

Jika proses migrasi telah selesai, Anda akan melihat pesan seperti dibawah ini:

![Pesan sukses migrasi Cloudways](https://img.penasihathosting.com/2025/May/Migrasi-telah-selesai-1024x685.webp "Pesan sukses migrasi Cloudways")

Sampai disini, Anda perlu melihat terlebih dahulu _website destination_ Anda dengan mengklik link domain sementara yang disediakan Cloudways.

Jika Anda tidak melihat perubahan apapun pada website destination Anda, maka Anda harus membersihkan / clear varnish server Anda terlebih dahulu.

Caranya:

Klik Servers seperti yang saya tunjukan pada screenshoot dibawah ini:

![Menu Servers di dashboard Cloudways](https://img.penasihathosting.com/2025/May/klik-servers-1024x433.webp "Menu Servers di dashboard Cloudways")

Lalu, klik managed services yang ada di sebelah kiri -> dan klik purge seperti yang saya arahkan pada gambar dibawah ini:

![Tombol purge Varnish di Cloudways](https://img.penasihathosting.com/2025/May/purge-varnish.webp "Tombol purge Varnish di Cloudways")

Setelah itu, harusnya website destination Anda sudah berubah.

Cek semua halaman website, Anda juga mungkin perlu log in ke wp-admin untuk mengecek apakah semua nya sudah OK.

### 3\. Menambahkan Domain ke Aplikasi WordPress Anda

Pada tahap ini, aplikasi WordPress Anda sudah berhasil terinstall, tetapi masih menggunakan nama domain sementara, seperti yang Anda lihat pada langkah sebelum nya di _website destination_.

Tentu saja Anda tidak akan menggunakan [domain](https://penasihathosting.com/direktori/kategori/domain/) sementara itu bukan? Yang terlihat seperti ini: _wordpress-362278-1127190.cloudways.com._ Maka Anda perlu menambahkan domain Anda sendiri dan menghubungkan nya ke server Cloudways.

Caranya:

**MENAMBAHKAN DOMAIN**

buka menu **Domain Management** yang ada di _sidebar_ sebelah kiri (1), kemudian klik tombol "Add Domain"(2)

![Menu Domain Management di Cloudways](https://img.penasihathosting.com/2025/May/Buka-tab-domain-management-1024x645.webp "Menu Domain Management di Cloudways")

Kemudian masukkan nama domain Anda, klik tombol "+ Add Domain".

Jika Anda menggunakan 'www' pada domain Anda, maka tambahkan 'www'.

![Form penambahan domain di Cloudways](https://img.penasihathosting.com/2025/May/tambahkan-nama-domain.webp "Form penambahan domain di Cloudways")

Kemudian, jadikan domain yang baru Anda tambahkan tersebut sebagai 'Make Primary'

![Tombol Make Primary domain di Cloudways](https://img.penasihathosting.com/2025/May/make-primary.webp "Tombol Make Primary domain di Cloudways")

**MENAMBAHKAN A RECORD DI DNS MANAGEMENT**

Selanjutnya, tergantung dari provider tempat Anda membeli domain, Anda perlu menambahkan A Record pada DNS Management domain Anda.

- Type = A Record
- Host = @ (kecuali Anda ingin nama domain Anda menggunakan "www", maka isi dengan "www")
- Value = Isi dengan IP Public server Anda (IP yang sama yang Anda copy ke form migrasi sebelumnya)
- TTL = Biarkan saja apa adanya atau set Automatic

<figure>

![Form pengisian A Record di Cloudflare](https://img.penasihathosting.com/2025/May/cara-mengisi-A-record-di-Cloudflare.webp "Form pengisian A Record di Cloudflare")

<figcaption>

_Cara menambahkan A record di DNS management Cloudflare_

</figcaption>

</figure>

Pastikan juga Anda menambahkan A record untuk versi 'www' website Anda dan diarahkan ke IP yang sama. Nantinya, versi 'www' ini akan otomatis ter-redirect ke website Anda yang versi 'non www'.

**Please Note:**

Secara default, Cloudways tidak menggunakan NS, jadi yang Anda perlu tambahkan/ganti **hanyalah A Record saja.**

Sampai disini, Anda hanya perlu menunggu. Jika Anda sudah benar mengisi A Record, harusnya kurang dari 15 menit atau paling lama 24 jam domain Anda sudah terhubung dengan server.

Sampai disini, Anda hanya perlu menunggu.

Jika Anda sudah benar mengisi A Record, harusnya kurang dari 1 jam atau bisa hanya dalam hitungan menit atau paling lama 24 jam, domain Anda sudah terhubung dengan server.

Anda bisa cek status DNS Anda di [DNSChecker](https://dnschecker.org/).

### 4\. Install SSL

Setelah domain Anda terhubung ke server, Anda wajib menginstall SSL (gratis) untuk keamanan website Anda.

Caranya:

- Buka menu "SSL Certificate" (1)
- Pilih Lets Encrypt (2)
- Masukkan email Anda (3) - Tidak perlu harus sama dengan email CloudwaysMasukkan nama domain (4)
- Klik tombol "INSTALL CERTIFICATE" (5)

![Form instalasi SSL di Cloudways](https://img.penasihathosting.com/2025/May/install-ssl.webp "Form instalasi SSL di Cloudways")

Juga merupakan langkah yang bijak untuk mem-verifikasi bahwa semua perubahan yang Anda lakukan sudah berhasil terpalikasi. Test website Anda menggunakan [WhyNoPadLock](https://www.whynopadlock.com/) untuk melihat apakah SSL sudah valid dan force HTTPS sudah benar.

![Hasil verifikasi SSL di WhyNoPadLock](https://img.penasihathosting.com/2025/May/verifikasi-SSL-1024x740.webp "Hasil verifikasi SSL di WhyNoPadLock")

Saya juga menyarankan Anda untuk melakukan test redirect map pada domain Anda menggunakan [https://httpstatus.io/](https://httpstatus.io/). Test ini berguna untuk memastikan bahwa tidak ada _redirect chain_ yang bisa merusak _technical_ SEO website Anda.

Harusnya, hasil nya seperti ini (ini berlaku untuk penggunaan domain non 'www'), jika tidak sama, maka kemungkinan besar terjadi kesalahan saat Anda memasukkan A Record ke DNS Management domain Anda.

![Hasil test redirect map di httpstatus.io](https://img.penasihathosting.com/2025/May/cek-status--1024x312.webp "Hasil test redirect map di httpstatus.io")

Semoga tutorial cara migrasi WordPress ke Cloudways ini membantu. Jika Anda menemui kesulitan atau tidak yakin dengan bagian manapun dalam tutorial ini, jangan ragu untuk bertanya melalui kolom komentar yang ada dibawah. 