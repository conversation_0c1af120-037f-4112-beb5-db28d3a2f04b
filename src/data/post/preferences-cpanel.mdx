---
title: "Mengupdate Preferences di cPanel: Password, Kontak, dan User"
publishDate: 2016-12-13
updateDate: 2025-05-21
categories: 
  - "Panduan Lengkap"
tags: 
  - "panduan-cpanel"
  - "cpanel"
  - "hosting"
image: https://img.penasihathosting.com/2025/cpanel/dashboard.webp
excerpt: "Cara mengatur preferensi, mengganti password, dan mengelola user di cPanel. Panduan lengkap untuk mengoptimalkan pengaturan cPanel Anda."
metadata:
  title: "Mengupdate Preferences di cPanel: Password, Ko<PERSON><PERSON>, dan User (Edisi 2025)"
  description: "Pelajari cara mengatur preferensi, mengganti password, dan mengelola user di cPanel dengan panduan lengkap dan informasi terbaru tahun 2025."
  guideId: "cpanel-guide"
  chapterIndex: 3
  chapterTitle: "Mengupdate Preferences"
---

# Mengupdate Preferences di cPanel: Password, <PERSON><PERSON><PERSON>, dan <PERSON><PERSON> [antarmuka dasar cPanel](/antarmuka-cpanel/), langkah penting selanjutnya adalah mengatur preferensi akun Anda. Pengaturan ini mencakup keamanan akun, informasi kontak, dan manajemen pengguna.

## Mengakses Preferences di cPanel

Ketika Anda log in ke cPanel untuk pertama kalinya, pastikan Anda mengunjungi bagian "preferences" untuk mengupdate informasi kontak dan mengganti password yang Anda gunakan.

![Menu Preferences di cPanel](https://img.penasihathosting.com/2025/cpanel/Preferences-1024x435.webp)

Di bagian Preferences, Anda akan menemukan beberapa opsi penting:

1. **Password & Security** - Untuk mengganti password dan mengatur keamanan akun
2. **Contact Information** - Untuk mengupdate informasi kontak Anda
3. **User Manager** - Untuk mengelola user tambahan pada akun cPanel Anda
4. **Style** - Untuk mengubah tampilan cPanel
5. **Localization** - Untuk mengatur bahasa dan zona waktu

Mari kita bahas masing-masing opsi secara detail.

## Mengganti Password

Saya sangat merekomendasikan untuk secara reguler mengganti password setidaknya sekali dalam 3 bulan untuk melindungi atau mencegah website Anda dari hacking.

Pertama-tama klik "password & security" pada bagian preferences.

Kemudian Anda akan melihat halaman dengan tampilan seperti dibawah ini.

![Halaman pengaturan Password & Security di cPanel](https://img.penasihathosting.com/2025/cpanel/password-982x1024.webp)

Sebelum Anda mengganti password dengan yang password yang baru, pastikan Anda sudah mengetahui password lama Anda. 

Saya juga merekomendasikan menggunakan "password generator" untuk mendapatkan password yang kuat.

![Antarmuka password generator di cPanel](https://img.penasihathosting.com/2025/cpanel/pass-generator.webp)

### Praktik Terbaik untuk Password cPanel

Berikut beberapa praktik terbaik untuk membuat password yang kuat:

1. **Gunakan minimal 12 karakter** - Semakin panjang, semakin baik
2. **Kombinasikan huruf besar dan kecil** - Misalnya "AbCdEf"
3. **Sertakan angka** - Misalnya "123456"
4. **Tambahkan karakter khusus** - Seperti "!@#$%^&*()"
5. **Hindari informasi pribadi** - Jangan gunakan nama, tanggal lahir, atau informasi yang mudah ditebak
6. **Jangan gunakan password yang sama** - Gunakan password unik untuk cPanel

Pastikan Anda menyimpan password baru Anda di tempat yang aman.

Rekomendasi saya adalah menggunakan aplikasi penyimpanan password seperti [1Password](https://1password.com/), [LastPass](https://www.lastpass.com/) atau [BitWarden](https://bitwarden.com) (gratis).

### Bagaimana Bila Anda Kehilangan Password Lama Anda?

- Apabila Anda lupa mencatat password Anda, kembali pada dashboard account hosting Anda atau cek email detail account cPanel yang dikirimkan oleh penyedia web hosting setelah Anda membeli hosting.

- Apabila emailnya sudah terhapus atau Anda tidak dapat menemukan password lama Anda, jangan panik! Hubungi support penyedia web hosting Anda melalui live chat/tiket bantuan dan tanyakan soal password cPanel lama Anda.

## Mengupdate Informasi Kontak

Umumnya, email yang Anda gunakan pada saat pendaftaran hosting akan menjadi email utama pada cPanel Anda, namun demi alasan keamanan, saya menyarankan Anda untuk menggunakan email yang berbeda untuk menerima update dari cPanel.

Untuk mengupdate informasi kontak, klik "Contact Information" pada bagian preferences dan Anda akan melihat tampilan seperti dibawah ini.

![Halaman Contact Information di cPanel](https://img.penasihathosting.com/2025/cpanel/Mengganti-kontak-1024x930.webp)

Saya merekomendasikan Anda untuk menceklis semua box yang ada pada "Contact Preferences", agar Anda menerima pemberitahuan bilamana ada sesuatu yang mencurigakan terjadi, seperti orang lain yang mengganti password Anda atau terjadi masalah seperti penggunaan disk space/pemakaian bandwidth yang melebihi kapasitas.

### Pentingnya Informasi Kontak yang Akurat

Memiliki informasi kontak yang akurat sangat penting karena beberapa alasan:

1. **Notifikasi Keamanan** - Anda akan diberitahu jika ada aktivitas mencurigakan
2. **Pemberitahuan Sistem** - Informasi tentang pemeliharaan server atau masalah
3. **Pemulihan Akun** - Diperlukan untuk memulihkan akses jika Anda lupa password
4. **Komunikasi Penting** - Pemberitahuan tentang pembaruan atau perubahan kebijakan

## Menambahkan User Baru

Katakanlah Anda mempunyai web developer atau anggota dalam team yang membantu Anda dalam pembuatan atau mengelola website, dan membutuhkan akses untuk membuat suatu perubahan dalam cPanel Anda.

Dihalaman ini, Anda dapat melihat seluruh account yang telah Anda buat pada cPanel Anda, dan disamping kanan account-account tersebut, terdapat tiga ikon:

- **Ikon kotak surat** mengindikasikan bahwa account ini memiliki email account yang terintegrasi dengannya.

- **Ikon truk pengiriman** mengindikasikan bahwa account ini memiliki akses ke FTP (file transfer protocol)

- **Ikon disc drive** mengindikasikan bahwa account ini dapat menggunakan storage/web disk yang tersedia pada cPanel Anda.

Anda dapat dengan mudah mengedit, menghapus atau mengganti password dari masing-masing account dengan mengklik pilihan-pilihan yang terdapat dibawah nama account.

Untuk menambahkan user baru, klik "Add User" yang terdapat disudut sebelah kanan atas atau yang dilingkari merah pada gambar dibawah ini.

![Halaman User Manager di cPanel](https://img.penasihathosting.com/2025/cpanel/User-manager-1024x629.webp)

Lalu Anda akan diarahkan ke halaman berikut:

![Formulir untuk menambahkan user baru di cPanel](https://img.penasihathosting.com/2025/cpanel/menambahkan-user-baru-di-cPanel-1024x1009.webp)

Di halaman ini, untuk menambahkan user baru Anda perlu untuk mengisi full name user, username, domain yang terintegrasi dengan user baru tersebut, kemudian contact email address yang mana nantinya sistem akan mengirimkan pemberitahuan, seperti konfirmasi permintaan reset password.

Anda juga diminta untuk membuat password untuk user tersebut, dan pastikan untuk membuat password yang berbeda dengan user-user lainnya dalam account Anda.

Setelah itu, dibawahnya terdapat bagian yang dinamakan "services", yang mana pada bagian ini Anda perlu untuk menentukan pengaturan untuk user baru tersebut, seperti email, FTP dan web disk.

![Bagian pengaturan Services saat menambah user baru di cPanel](https://img.penasihathosting.com/2025/cpanel/Services-1024x438.webp)

- **Email** memungkinkan Anda untuk mengaktifkan/mendisable email account untuk pengguna/users (meskipun Anda perlu untuk men set account pengguna terlebih dahulu) dan mengatur berapa limit yang diperlukan untuk masing-masing account, misalnya 1024mb.

- **FTP** memungkinkan Anda untuk mengaktifkan/mendisable account untuk dapat mengakses File Transfer Protocol, dimana pengguna dapat mengupload file ke website Anda. Andapun dapat memilih didirectory mana pengguna tersebut memiliki akses, dan mengatur berapa limit space yang diperlukan (sama seperti limit email), katakanlah 1024mb.

- **Web Disk** ini seharusnya diaktifkan hanya untuk pengguna utama, dalam arti Anda sebagai pemilik website atau untuk account pengguna "top-level admin". Dimana pengguna yang memiliki akses ke web disk dapat diberikan dua pilihan "permissions", yaitu Read-White (memberikan izin kepada pengguna untuk dapat melakukan apapun termasuk mendelete suatu file dalam directory tertentu), sementara Read-Only (pengguna hanya dapat membaca files dan mendownloadnya).

Ketika Anda sudah selesai mengatur semuanya, klik "Create" atau "Create and Add Another User" yang terdapat disebelah kiri bawah halaman untuk menyimpan pengaturan account baru Anda.

### Praktik Terbaik Manajemen User

Berikut beberapa praktik terbaik saat mengelola user di cPanel:

1. **Prinsip Hak Akses Minimal** - Berikan user hanya akses yang mereka butuhkan
2. **Gunakan Password Unik** - Setiap user harus memiliki password yang berbeda
3. **Audit Secara Berkala** - Periksa user yang aktif dan hapus yang tidak diperlukan
4. **Dokumentasikan Akses** - Catat siapa yang memiliki akses apa
5. **Batasi Akses Admin** - Jangan berikan akses admin penuh kecuali benar-benar diperlukan

## Pengaturan Preferensi Lainnya

Selain pengaturan yang telah dibahas di atas, ada beberapa pengaturan preferensi lain yang mungkin ingin Anda sesuaikan:

### Pengaturan Bahasa

cPanel tersedia dalam berbagai bahasa. Untuk mengubah bahasa:

1. Klik "Change Language" di bagian Preferences
2. Pilih bahasa yang Anda inginkan dari daftar
3. Klik "Change"

### Pengaturan Zona Waktu

Untuk mengubah zona waktu:

1. Klik "Change Timezone" di bagian Preferences
2. Pilih zona waktu yang sesuai dengan lokasi Anda
3. Klik "Change"

### Pengaturan Style

Untuk mengubah tampilan cPanel:

1. Klik "Change Style" di bagian Preferences
2. Pilih style yang Anda inginkan
3. Klik "Change"

## Kesimpulan

Mengupdate preferences di cPanel adalah langkah penting untuk memastikan keamanan dan kenyamanan penggunaan cPanel Anda. Dengan mengatur password yang kuat, memperbarui informasi kontak, dan mengelola user dengan bijak, Anda dapat melindungi website Anda dari akses yang tidak sah dan memastikan bahwa Anda selalu mendapatkan informasi penting terkait hosting Anda.

Pada [bab berikutnya](/domain-cpanel/), kita akan mempelajari cara mengelola domain di cPanel, termasuk cara menambahkan domain baru dan mengatur redirects.

<div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg border-l-4 border-primary my-8">
  <h4 class="text-lg font-bold text-blue-800 dark:text-blue-300 mb-2">Tips Keamanan</h4>
  <p class="text-sm text-gray-700 dark:text-gray-300">
    Selalu gunakan password yang kuat dan unik untuk cPanel Anda. Jangan pernah menggunakan password yang sama untuk layanan yang berbeda. Pertimbangkan untuk mengaktifkan autentikasi dua faktor jika tersedia untuk lapisan keamanan tambahan.
  </p>
</div>
