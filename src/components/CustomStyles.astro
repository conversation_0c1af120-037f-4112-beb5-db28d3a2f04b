---

---

<style is:inline>
  @font-face {
    font-family: 'Proxima Soft';
    src: url('/fonts/proximasoft.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Proxima Soft';
    src: url('/fonts/proximasoftbold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }

  :root {
    --aw-font-sans: 'Proxima Soft', sans-serif;
    --aw-font-serif: 'Proxima Soft', sans-serif;
    --aw-font-heading: 'Proxima Soft', sans-serif;

    --aw-color-primary: oklch(14.5% 0.01 270);
    --aw-color-secondary: oklch(20.5% 0 0);
    --aw-color-accent: oklch(50.5% 0.213 27.518);
    --aw-color-accent-hover: oklch(50.5% 0.213 27.518 / 0.8);

    --aw-color-text-heading: rgb(0 0 0);
    --aw-color-text-default: rgb(16 16 16);
    --aw-color-text-muted: rgb(16 16 16 / 66%);
    --aw-color-bg-page: rgb(255 255 255);

    --aw-color-bg-page-dark: rgb(3 6 32);

    ::selection {
      background-color: lavender;
    }
  }

  .dark {
    --aw-font-sans: 'Proxima Soft', sans-serif;
    --aw-font-serif: 'Proxima Soft', sans-serif;
    --aw-font-heading: 'Proxima Soft', sans-serif;

    --aw-color-primary: rgb(1 97 239);
    --aw-color-secondary: rgb(1 84 207);
    --aw-color-accent: oklch(50.5% 0.213 27.518);
    --aw-color-accent-hover: oklch(50.5% 0.213 27.518 / 0.8);

    --aw-color-text-heading: rgb(247, 248, 248);
    --aw-color-text-default: rgb(229 236 246);
    --aw-color-text-muted: rgb(229 236 246 / 66%);
    --aw-color-bg-page: rgb(3 6 32);

    ::selection {
      background-color: black;
      color: snow;
    }
  }
</style>

<style is:global>
  .btn-primary {
    background-color: var(--aw-color-primary);
    color: white !important; /* Ensure text is white */
    text-decoration: none !important;
    padding: 0.5rem 1rem; /* Basic padding */
    border-radius: 0.375rem; /* Rounded corners, adjust as needed */
    text-align: center;
    transition: background-color 0.2s ease-in-out;
  }

  .btn-primary:hover {
    /* You can define a hover state, e.g., slightly darken or lighten the primary color */
    /* For now, let's use the accent color for hover as an example, or you can create a new variable */
    background-color: var(--aw-color-accent);
  }

  /* You might want to define .btn-secondary here too if it's not elsewhere */
  .btn-secondary {
    background-color: var(--aw-color-secondary);
    color: var(--aw-color-text-default); /* Default text color, might need adjustment based on secondary color's lightness */
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    text-align: center;
    border: 1px solid var(--aw-color-secondary); /* Example: border for secondary */
  }

  .btn-secondary:hover {
    background-color: var(--aw-color-text-muted); /* Example hover */
  }

  .prose a {
    color: var(--aw-color-accent);
    font-weight: 700;
  }
</style>
