export interface TableData {
  headers: string[];
  rows: string[][];
  caption?: string;
}

export interface HostingReview {
  providerName: string;
  highlights: string[];
  website: string;
  reviewUrl: string;
  pricing: string;
  couponCode?: string;
  couponDetails?: string;
  description: (string | 
    { type: 'heading'; level: 1 | 2 | 3 | 4 | 5 | 6; text: string } |
    { type: 'table'; data: TableData }
  )[];
  performanceMetrics?: {
    responseTime?: string;
    uptime?: string;
  };
  pros: string[];
  cons: string[];
  image?: string;
  affiliateLink?: string;
  whyChooseTitle?: string | false; // false to hide the title completely
  boxContent?: {
    paragraphs?: string[];
    lists?: {
      title?: string;
      items: string[];
    }[];
    paragraphsAfterLists?: string[];
  };
}

export interface HostingReviewType {
  name: string;
  slug: string;
  logo: string;
  featuredImage: string;
  shortDescription: string;
  rating: number;
  pricing: {
    monthly: number;
    yearly: number;
    discount: number;
    discountCode: string;
    features: string[];
  };
  features: {
    storage: string;
    bandwidth: string;
    domains: string;
    email: string;
    database: string;
    ssl: boolean;
    backup: boolean;
    migration: string;
    controlPanel: string;
    moneyBackGuarantee: string;
  };
  pros: string[];
  cons: string[];
  affiliateLink: string;
  fullReviewLink: string;
  performance: {
    uptime: number;
    speed: number;
    lastUpdated: string;
  };
  pricingTable: Array<{
    period: string;
    price: number;
    renewal: number;
    discount: number;
  }>;
  performanceData: {
    labels: string[];
    uptime: number[];
    speed: number[];
  };
}
