---
import type { HostingReview as HostingReviewType } from './reviews/types';
import HostingRecommendations from '~/components/blog/HostingRecommendations.astro';
import HostingReview from './reviews/HostingReview.astro';
import HostingList from './HostingList.astro';
import HostingContent from './HostingContent.astro';
import FAQSection from './FAQSection.astro';

type FAQComponent = typeof FAQSection;

interface SidebarContent {
  title?: string;
  content?: unknown; // This will accept any content that can be rendered in Astro
}

interface Props {
  hostingList?: string[];
  reviews: HostingReviewType[];
  title?: string;
  intro?: string;
  paragraphs?: string[];
  showSidebar?: boolean;
  showFAQ?: boolean;
  showHostingList?: boolean;
  textClass?: string;
  sidebarContent?: SidebarContent;
  faqComponent?: FAQComponent; // Accept a custom FAQ component
}

const {
  hostingList = [],
  reviews,
  title = '',
  intro = '',
  paragraphs = [],
  showSidebar = true,
  showFAQ = true,
  showHostingList = hostingList.length > 0, // Only show if hostingList is provided
  textClass = 'text-gray-700 dark:text-gray-300',
  sidebarContent = null,
  faqComponent: CustomFAQ = null
} = Astro.props;

// Text classes are now passed through props
---

<section class="py-8 bg-white dark:bg-dark">
  <div class="max-w-global mx-auto px-4 sm:px-6">
    <div class="lg:grid lg:grid-cols-3 lg:gap-12">
      {/* Main Content - 2/3 Width */}
      <div class={showSidebar ? 'lg:col-span-2' : 'lg:col-span-3'}>
        <div class="prose dark:prose-invert max-w-none">
          <HostingContent title={title} intro={intro} paragraphs={paragraphs} textClass={textClass}>
            {showHostingList && hostingList.length > 0 && (
              <HostingList hostingList={hostingList} textClass={textClass} />
            )}
          </HostingContent>
          
          {reviews.length > 0 && (
            <div class="mt-12">
              {reviews.map((review, index) => (
                <HostingReview review={review} index={index} />
              ))}
            </div>
          )}
          
          {showFAQ && (
            <div class="mt-12">
              {CustomFAQ ? <CustomFAQ /> : <FAQSection />}
            </div>
          )}
        </div>
      </div>
      
      {/* Sidebar - 1/3 Width - Conditionally rendered */}
      {showSidebar && (
        <div class="lg:col-span-1">
          <div class="sticky top-6">
            {sidebarContent ? (
              <>
                {sidebarContent.title && <h3 class="text-lg font-semibold mb-4">{sidebarContent.title}</h3>}
                {sidebarContent.content}
              </>
            ) : (
              <HostingRecommendations />
            )}
          </div>
        </div>
      )}
    </div>
  </div>
</section>
