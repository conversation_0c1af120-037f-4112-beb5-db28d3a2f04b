---
import FYINotice from '~/components/blog/FYINotice.astro';

interface Props {
  title?: string;
  content?: string;
  showReviewMethodology?: boolean;
  showSidebar?: boolean;
  defaultContent?: {
    paragraphs: string[];
  };
}

const { 
  title = "Bagaimana kami melakukan review",
  content,
  showReviewMethodology = true,
  showSidebar = true,
  defaultContent: customDefaultContent
} = Astro.props;

const finalContent = content || customDefaultContent?.paragraphs || [];

const reviewMethodology = [
  {
    title: "Membeli Web Hosting Populer",
    description: "<PERSON><PERSON> membeli 6 web hosting populer Indonesia untuk kebutuhan review.",
    icon: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />`
  },
  {
    title: "Setup Situs WordPress",
    description: "Men-setup WordPress dengan konfigurasi yang sama untuk pengetesan.",
    icon: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />`
  },
  {
    title: "Monitoring dan Pengujian",
    description: "Melakukan monitoring uptime pengujian Load Testing dan support.",
    icon: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />`
  },
  {
    title: "Mempublikasikan review",
    description: "Review di publikasikan setelah bahan penelitian selesai dikumpulkan.",
    icon: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />`
  }
];
---

<style is:inline>
  div.prose :global(li) {
    margin-top: 0;
    margin-bottom: 0;
  }
  div.prose :global(p) {
    margin-bottom: 1.5em;
  }
</style>

<section class="bg-white dark:bg-dark pb-12">
  <div class="max-w-global mx-auto px-4 sm:px-6">
    <div class="lg:grid lg:grid-cols-3 lg:gap-12">
      {/* Main Content - 2/3 Width */}
      <div class={showSidebar ? 'lg:col-span-2' : 'lg:col-span-3'}>
        <div class="prose prose-base dark:prose-invert prose-a:font-bold prose-a:text-[var(--aw-color-accent)] prose-a:hover:text-[var(--aw-color-accent-hover)] dark:prose-a:text-[var(--aw-color-accent)] dark:prose-a:hover:text-[var(--aw-color-accent-hover)] prose-img:rounded-md prose-headings:scroll-mt-[80px] max-w-none leading-normal">
          {typeof finalContent === 'string' ? (
            <Fragment set:html={finalContent} />
          ) : (
            <>
              {finalContent.map(paragraph => (
                <p>{paragraph}</p>
              ))}
            </>
          )}
        </div>

        {showReviewMethodology && (
          <div class="py-4 sm:py-6 bg-gray-50 dark:bg-gray-800 px-4 sm:px-6 mt-6 rounded-lg">
            <div class="flex flex-col md:flex-row items-center justify-between mb-6">
              <h2 class="text-xl font-bold text-gray-700 dark:text-gray-200">{title}</h2>
              <a href="#" class="text-primary hover:underline text-sm flex items-center mt-2 md:mt-0">
                Baca hasil penelitian →
              </a>
            </div>
        
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              {reviewMethodology.map((item) => (
                <div class="flex items-start">
                  <div class="mr-4 text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" set:html={item.icon} />
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-800 dark:text-gray-100 mb-1">{item.title}</h3>
                    <p class="text-gray-600 dark:text-gray-300 text-sm">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Sidebar - 1/3 Width - Conditionally rendered */}
      {showSidebar && (
        <div class="lg:col-span-1 mt-8 lg:mt-0">
          <div class="space-y-6">
            <FYINotice />
          </div>
        </div>
      )}
    </div>
  </div>
</section>