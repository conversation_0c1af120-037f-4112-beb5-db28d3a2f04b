---
// Data models only, no JSX/HTML in frontmatter!
const hostingProviders = [
  {
    name: 'IdCloudHost.com',
    logo: 'https://img.penasihathosting.com/2025/May/IdCloudHost.webp',
    url: 'https://penasihathosting.com/go/idcloudhost',
    features: [
      { text: 'Kecepatan server hosting tercepat ke #1', isPositive: true },
      { text: 'Harga hosting terjangkau + struktur harga yang adil', isPositive: true },
      { text: 'Rata-rata uptime 100% dalam 7 bulan terakhir', isPositive: true },
      { text: 'Layanan support yang cukup cepat', isPositive: true },
      { text: 'Tidak ada garansi hosting!', isPositive: false }
    ]
  },
  {
    name: 'KencengSolusindo.com',
    logo: 'https://img.penasihathosting.com/2025/May/logo-kenceng-solusindo.webp',
    url: 'https://penasihathosting.com/go/kencengsolusindo',
    features: [
      { text: 'Kecepatan server hosting tercepat ke #2', isPositive: true },
      { text: 'Rata-rata uptime sangat stabil dalam 7 bulan terakhir', isPositive: true },
      { text: 'Layanan support yang cepat', isPositive: true },
      { text: 'Harga hosting terjangkau + struktur harga yang adil', isPositive: true },
      { text: 'Tidak ada garansi hosting!', isPositive: false }
    ]
  },
  {
    name: 'Cloudways.com',
    logo: 'https://img.penasihathosting.com/2025/May/Cloudways-by-DO-Horizontal-Blue.webp',
    url: 'https://penasihathosting.com/go/cloudways',
    isSponsored: true,
    features: 'Cloudways adalah platform managed cloud hosting yang menawarkan fleksibilitas memilih provider cloud terkemuka (AWS, Google Cloud, DigitalOcean, Vultr, Linode), dilengkapi dengan stack teknologi teroptimasi, fitur keamanan berlapis, panel kontrol intuitif, dan dukungan 24/7 untuk memberikan pengalaman hosting yang powerful namun tetap mudah dikelola.'
  }
];

interface Props {
  title?: string;
}

const { title } = Astro.props;
---

<section class="py-4 bg-white dark:bg-dark">
  <div class="max-w-global mx-auto px-4 sm:px-6">
    <div class="max-w-full lg:max-w-[57ch] border-t-4 border-primary dark:border-primary pt-2 mb-8">
      <h2 class="text-2xl font-bold">{title}</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      {hostingProviders.map((provider) => (
        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700">
          {provider.isSponsored && (
            <div class="mb-2">
              <span class="text-xs text-gray-500 dark:text-gray-400">Sponsored:</span>
            </div>
          )}
          <img src={provider.logo} alt={provider.name} class="h-6 mb-6" />

          {Array.isArray(provider.features) ? (
            <div class="mb-4">
              <h3 class="font-bold text-gray-700 dark:text-gray-200 mb-2">Pros/Cons:</h3>
              <ul class="space-y-2">
                {provider.features.map((feature) => (
                  <li class="flex items-start">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class={`h-5 w-5 mr-2 flex-shrink-0 mt-0.5 ${feature.isPositive ? 'text-primary' : 'text-neutral-400'}`}
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      {feature.isPositive ? (
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      ) : (
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                      )}
                    </svg>
                    <span class="text-gray-600 dark:text-gray-300 text-sm">{feature.text}</span>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <p class="text-gray-600 dark:text-gray-300 text-sm mb-6">
              {provider.features}
            </p>
          )}

          <a
            href={provider.url}
            target="_blank"
            rel="nofollow noopener noreferrer"
            class={`block text-center py-3 px-4 ${provider.isSponsored ? 'bg-accent hover:bg-accent' : 'bg-primary hover:bg-accent'} text-white font-medium rounded-md transition-colors duration-300`}
          >
            {provider.name} →
          </a>
        </div>
      ))}
    </div>
  </div>
</section>
