---
interface Props {
  hostingList: string[];
  textClass?: string;
}

const { hostingList, textClass = 'text-gray-700 dark:text-gray-300' } = Astro.props;
---

<ul class={`list-disc pl-6 mb-6 space-y-1 [&>li]:my-0`}>
  {hostingList.map(host => {
    // Extract just the provider name (before the first hyphen) for the ID
    const providerName = host.split(' - ')[0].trim();
    const slug = providerName.toLowerCase().replace(/\s+/g, '-');
    return (
      <li class={textClass}>
        <a 
          href={`#review-${slug}`}
          class="hover:text-accent transition-colors hover:underline cursor-pointer"
        >
          {host}
        </a>
      </li>
    );
  })}
</ul>

<style>
  /* Smooth scroll behavior for all anchor links */
  html {
    scroll-behavior: smooth;
  }
  
  /* Add some padding to account for fixed header */
  [id^="review-"] {
    scroll-margin-top: 100px;
  }
</style>
