---
import type { HostingReview } from './types';
import { Icon } from 'astro-icon/components';

interface Props {
  review: HostingReview;
  index: number;
}

const { review, index } = Astro.props;
---

<article id={`review-${review.providerName.toLowerCase().replace(/\s+/g, '-')}`} class="mb-6 scroll-mt-24">
  <h3 class="text-2xl md:text-3xl font-extrabold mb-4" style="color:var(--aw-color-text-heading)">{index + 1}. {review.providerName}</h3>
  <div class="relative border-4 border-primary dark:border-primary rounded-xl overflow-visible mb-6 p-0 bg-white" style="background:var(--aw-color-bg-page)">
    <div class="grid md:grid-cols-2 gap-0 px-6 pb-2">
      {/* Konten Kiri */}
      <div>
        {review.whyChooseTitle !== false && (
          <h4 class="font-bold mb-3">
            {typeof review.whyChooseTitle === 'string' 
              ? review.whyChooseTitle 
              : `Mengapa memilih ${review.providerName}?`}
          </h4>
        )}
        
        {/* Highlights */}
        {review.highlights && review.highlights.length > 0 && (
          <ul class="list-disc pl-5 space-y-2 mb-6">
            {review.highlights.map((highlight, i) => (
              <li data-key={`highlight-${i}`}>{highlight}</li>
            ))}
          </ul>
        )}
        
        {/* Box Content - Optional */}
        {review.boxContent && (
          <div class="space-y-4 mb-6">
            {/* Paragraphs */}
            {review.boxContent.paragraphs && review.boxContent.paragraphs.length > 0 && (
              <div class="space-y-3">
                {review.boxContent.paragraphs.map((paragraph, i) => (
                  <p data-key={`box-para-${i}`} class="leading-relaxed">{paragraph}</p>
                ))}
              </div>
            )}
            
            {/* Lists */}
            {review.boxContent.lists && review.boxContent.lists.map((list, listIndex) => (
              <div data-key={`box-list-${listIndex}`} class="mt-3">
                {list.title && <h5 class="font-semibold text-sm mb-2">{list.title}</h5>}
                <ul class="list-disc pl-5 space-y-1">
                  {list.items.map((item, itemIndex) => (
                    <li data-key={`box-list-${listIndex}-item-${itemIndex}`}>{item}</li>
                  ))}
                </ul>
              </div>
            ))}
            
            {/* Paragraphs after lists */}
            {review.boxContent.paragraphsAfterLists && review.boxContent.paragraphsAfterLists.map((paragraph, i) => (
              <p data-key={`box-after-list-para-${i}`} class="text-sm mt-3 leading-relaxed">
                {paragraph}
              </p>
            ))}
          </div>
        )}
        {/* Tombol CTA */}
        <a
          href={review.affiliateLink || `https://${review.website}`}
          target="_blank"
          rel="nofollow noopener"
          class="font-semibold rounded-sm shadow py-4 px-8 text-mediium mb-4 transition-colors text-center btn-primary"
        >
          Kunjungi {review.providerName}
          <Icon name="tabler:external-link" class="w-4 h-4 ml-2" />
        </a>
      </div>
      {/* Gambar Kanan */}
      <div class="p-4 min-h-[220px]">
        {review.image ? (
          <img src={review.image} alt={review.providerName} class="max-w-full max-h-60 rounded-lg shadow" />
        ) : (
          <img src="/src/assets/images/providers/idcloudhost-logo.png" alt={review.providerName} class="max-w-xs max-h-48 rounded-lg shadow" />
        )}
      </div>
    </div>
    <div class="px-8 pb-8">
      <div>
        <span class="font-bold">Harga:</span> <span>mulai {review.pricing}</span>
      </div>
      {review.couponCode && (
        <div class="mt-1">
          <span class="font-bold">Kode kupon:</span> <span class="font-bold">{review.couponCode}</span> {review.couponDetails && <span>{review.couponDetails}</span>}
        </div>
      )}
    </div>
   </div>
  <div class="prose dark:prose-invert max-w-none prose-table:my-0">
    <div class="mb-2 space-y-4">
      {review.description.map((item) => {
        if (typeof item === 'string') {
          // Render regular paragraph
          return <p>{item}</p>;
        } else if (item.type === 'heading') {
          // Render heading based on level
          const HeadingTag = `h${item.level}` as const;
          return <HeadingTag class="font-semibold mt-6 mb-2">{item.text}</HeadingTag>;
        } else if (item.type === 'table') {
          // Render table
          return (
            <div class="overflow-x-auto my-0 [&_table]:my-0">
              <table class="min-w-full border border-gray-200 dark:border-gray-700">
                <thead>
                  <tr>
                    {item.data.headers.map(header => (
                      <th class="px-4 py-2 border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 font-semibold text-left">
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {item.data.rows.map((row, rowIndex) => (
                    <tr class={rowIndex % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'}>
                      {row.map(cell => (
                        <td class="px-4 py-2 border border-gray-200 dark:border-gray-700">
                          {cell}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
              {item.data.caption && (
                <div class="text-sm text-gray-500 dark:text-gray-400 mt-2 text-right">
                  {item.data.caption}
                </div>
              )}
            </div>
          );
        }
        return null;
      })}
    </div>
    <div class="m-4 mb-8 inline-flex flex-col">
      <a
        href={review.affiliateLink || `https://${review.website}`}
        class="inline-flex items-center justify-center px-8 py-4 font-bold rounded-lg transition-colors btn-primary"
        target="_blank"
        rel="nofollow noopener"
      >
        Host di {review.providerName} {review.couponCode && <span class="ml-1">dengan {review.couponCode.includes('%') ? review.couponCode : '40% OFF'}</span>}
        <Icon name="tabler:external-link" class="w-4 h-4 ml-2" />
      </a>
      <a
        href={review.reviewUrl}
        class="text-primary font-medium hover:underline mt-2"
        target="_blank"
      >
        Baca review lengkap {review.providerName}
      </a>
    </div>
    
    <div class="flex flex-col md:flex-row gap-6 mb-2">
      <div class="flex-1">
        <h4 class="text-lg font-semibold mb-3 flex items-center">
          <Icon name="tabler:thumb-up" class="w-5 h-5 mr-2" />
          Kelebihan
        </h4>
        <ul class="list-none pl-0 space-y-0">
          {review.pros.map(pro => (
            <li class="flex items-start">
              <span class="mr-2">
                <Icon name="tabler:circle-check" class="w-5 h-5" />
              </span>
              <span style="margin-top: -5px;">{pro}</span>
            </li>
          ))}
        </ul>
      </div>
      <div class="flex-1">
        <h4 class="text-lg font-semibold mb-3 flex items-center" style="color:var(--aw-color-secondary)">
          <Icon name="tabler:thumb-down" class="w-5 h-5 mr-2" />
          Kekurangan
        </h4>
        <ul class="list-none pl-0 space-y-0">
          {review.cons.map(con => (
            <li class="flex items-start">
              <span class="mr-2">
                <Icon name="tabler:circle-x" class="w-5 h-5" />
              </span>
              <span style="margin-top: -5px;">{con}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  </div>
</article>
