import type { HostingReview } from "../../types";

export const hostinganid: HostingReview = {
  providerName: "Hostingan ID",
  website: "www.Hostingan.id",
  reviewUrl: "#", // Placeholder, please update if you have a specific review URL
  pricing: "Rp 25.000/bulan (paket Cloud Hosting 'Mini', paket minimal yang saya rekomendasikan)",
  highlights: [
    "Rata-rata uptime yang cukup stabil dengan persentase 99,917%",
    "Memiliki struktur harga hosting yang adil (harga bulanan sama dengan harga tahunan)",
  ],
  description: [
    "Hostingan ID adalah provider baru yang saya review di tahun 2024 lalu. Sama seperti Dewabiz dan <PERSON> Solusindo, Hostingan ID juga berdiri pada tahun 2016. Mereka mengklaim sudah melayani lebih dari 20 ribu pelanggan.",
    "<PERSON><PERSON>, bagaimana sebenarnya performa mereka?",
    "Pertama, berdasarkan monitoring sepanjang tahun 2024 lalu, saya menemukan bahwa Hostingan ID memiliki uptime yang cukup stabil dengan persentase 99,917% (terstabil ke #2). <PERSON><PERSON>, tidak sampai kepada jaminan uptime 99,95% seperti yang mereka janjikan.",
    "Wajar jika mereka berani memberikan jaminan uptime 99,95%, sementara kebanyakan provider lain hanya berani sampai 99,90% saja.",
    "Kedua, dari hasil pengujian load testing, kecepatan response server mereka mengalami kegagalan sehingga tidak berhasil memberikan data yang valid. Hal ini menjadi salah satu kelemahan utama dari Hostingan ID.",
    "Selain itu, sama seperti Jagoan Hosting, Dracoola dan Kenceng Solusindo, Hostingan ID memiliki struktur harga yang menurut saya sangat adil. Anda dapat berlangganan hosting secara bulanan, enam bulanan, atau tahunan dengan harga yang sama. Dengan demikian, Anda tidak perlu khawatir terkena biaya tambahan jika memilih langganan jangka pendek.",
    "Kekurangan yang cukup menonjol dalam catatan saya adalah layanan support mereka yang agak lambat dan kurang inisiatif. Selain itu, mereka tidak memiliki fitur backup otomatis seperti kebanyakan provider lain, sehingga jika Anda membutuhkan fitur ini, Anda mungkin perlu menyiapkan solusi backup sendiri.",
    "Perlu dicatat bahwa Hostingan ID mengalami penurunan rank dari #2 ke #6 pada update kali ini.",
  ],
  affiliateLink: "https://penasihathosting.com/go/hostinganid",
  pros: [
    "Rata-rata uptime yang cukup stabil (terstabil ke #2)",
    "Harga hosting dengan struktur harga yang adil",
    "Ada gratis domain jika berlangganan secara tahunan",
  ],
  cons: [
    "Layanan support yang lambat dan kurang inisiatif",
    "Tidak ada garansi hosting / refund",
    "Tidak ada penawaran gratis migrasi hosting",
    "Paket Hosting Murah tidak menggunakan cPanel",
    "Belum menggunakan fitur backup otomatis",
    "Frekuensi backup terbatas",
    "Mengalami penurunan rank dari #2 ke #6 pada update kali ini",
  ],
  image: 'https://img.penasihathosting.com/2025/May/review-hostingan-id.webp' 
}; 