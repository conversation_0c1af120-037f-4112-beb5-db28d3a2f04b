---
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import Timeline from '~/components/ui/Timeline.astro';
import Headline from '~/components/ui/Headline.astro';
import Image from '~/components/common/Image.astro';
import type { Steps as Props } from '~/types';

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline = await Astro.slots.render('tagline'),
  items = [],
  image = await Astro.slots.render('image'),
  isReversed = false,

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-5xl ${classes?.container ?? ''}`} bg={bg}>
  <div class:list={['flex flex-col gap-8 md:gap-12', { 'md:flex-row-reverse': isReversed }, { 'md:flex-row': image }]}>
    <div class:list={['md:py-4 md:self-center', { 'md:basis-1/2': image }, { 'w-full': !image }]}>
      <Headline
        title={title}
        subtitle={subtitle}
        tagline={tagline}
        classes={{
          container: 'text-left rtl:text-right',
          title: 'text-3xl lg:text-4xl',
          ...((classes?.headline as object) ?? {}),
        }}
      />
      <Timeline items={items} classes={classes?.items as Record<string, never>} />
    </div>
    {
      image && (
        <div class="relative md:basis-1/2">
          {typeof image === 'string' ? (
            <Fragment set:html={image} />
          ) : (
            <Image
              class="inset-0 object-cover object-top w-full rounded-md shadow-lg md:absolute md:h-full bg-gray-400 dark:bg-slate-700"
              widths={[400, 768]}
              sizes="(max-width: 768px) 100vw, 432px"
              width={432}
              height={768}
              layout="cover"
              src={image?.src}
              alt={image?.alt || ''}
            />
          )}
        </div>
      )
    }
  </div>
</WidgetWrapper>
