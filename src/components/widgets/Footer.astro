---
import { SITE } from 'astrowind:config';
import { getHomePermalink } from '~/utils/permalinks';

interface Link {
  text?: string;
  href?: string;
  ariaLabel?: string;
  target?: string;
  rel?: string;
  isImage?: boolean;
}

interface Links {
  title?: string;
  links: Array<Link>;
}

export interface Props {
  links: Array<Links>;
  secondaryLinks: Array<Link>;
  socialLinks: Array<Link>;
  footNote?: string;
  theme?: string;
}

const { secondaryLinks = [], links = [], footNote = '', theme = 'light' } = Astro.props;
---

<footer class:list={[{ dark: theme === 'dark' }, 'relative border-t border-gray-200 dark:border-slate-800 not-prose']}>
  <div class="dark:bg-dark absolute inset-0 pointer-events-none" aria-hidden="true"></div>
  <div
    class="relative max-w-global mx-auto px-4 sm:px-6 dark:text-slate-300 intersect-once intersect-quarter intercept-no-queue motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
  >
    <div class="grid grid-cols-12 gap-8 py-12">
      <div class="col-span-12 lg:col-span-5">
        <div class="mb-4">
          <a class="inline-block font-bold text-2xl text-primary-600 dark:text-primary-400 hover:opacity-80 transition-opacity" href={getHomePermalink()}>{SITE?.name}</a>
        </div>
        <p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
          Penasihat Hosting adalah website yang independen. Bukan merupakan kesatuan atau bagian dari provider hosting manapun. Juga, tidak menerima uang sponsor atau semacam nya untuk mempublikasikan review.
        </p>
        <div class="flex flex-wrap gap-2">
          {
            secondaryLinks.map(({ text, href }) => (
              <a
                class="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:underline transition-colors"
                href={href}
                set:html={text}
              />
            ))
          }
        </div>
      </div>
      {
        links.map(({ title, links }) => (
          <div class="col-span-6 md:col-span-3">
            <h3 class="text-gray-800 dark:text-gray-100 font-semibold text-sm mb-3">{title}</h3>
            {links && Array.isArray(links) && links.length > 0 && (
              <ul class:list={[title === 'Alat Penelitian' ? 'grid grid-cols-2 gap-4' : 'space-y-3']}>
                {links.map(({ text, href, ariaLabel, target, rel, isImage }) => (
                  <li class="w-full">
                    {isImage ? (
                      <a
                        class="block w-full hover:opacity-80 transition-opacity"
                        href={href}
                        aria-label={ariaLabel}
                        target={target}
                        rel={rel}
                      >
                        <div class="w-full" set:html={text} />
                      </a>
                    ) : (
                      <a
                        class="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:underline transition-colors text-sm"
                        href={href}
                        aria-label={ariaLabel}
                      >
                        {text}
                      </a>
                    )}
                  </li>
                ))}
              </ul>
            )}
          </div>
        ))
      }
    </div>
    <div class="border-t border-gray-200 dark:border-gray-800 pt-2 mt-4 w-global">
      <div class="w-full">
        <div class="text-sm text-gray-600 dark:text-gray-400 w-global">
          <p class="leading-relaxed text-center">
            <Fragment set:html={footNote} />
          </p>
        </div>
      </div>
    </div>
  </div>
</footer>
