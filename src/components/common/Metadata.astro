---
import merge from 'lodash.merge';
import { AstroSeo } from '@astrolib/seo';

import type { Props as AstroSeoProps } from '@astrolib/seo';

import { SITE, METADATA, I18N } from 'astrowind:config';
import type { MetaData, MetaDataRobots } from '~/types';
import { getCanonical } from '~/utils/permalinks';

import { adaptOpenGraphImages } from '~/utils/images';

export interface Props extends MetaData {
  dontUseTitleTemplate?: boolean;
}

const {
  title,
  ignoreTitleTemplate = false,
  canonical = String(getCanonical(String(Astro.url.pathname))),
  robots = {},
  description,
  openGraph = {},
  twitter = {},
} = Astro.props;

// Manually handle external OpenGraph image
let externalOgImageUrl: string | null = null;
let processedOpenGraph = JSON.parse(JSON.stringify(openGraph));

if (Array.isArray(processedOpenGraph?.images) && processedOpenGraph.images.length > 0) {
  const firstImage = processedOpenGraph.images[0];
  if (typeof firstImage.url === 'string' && (firstImage.url.startsWith('http://') || firstImage.url.startsWith('https://'))) {
    externalOgImageUrl = firstImage.url;
    processedOpenGraph.images = processedOpenGraph.images.slice(1);
  }
}

const seoProps: AstroSeoProps = merge(
  {
    title: '',
    titleTemplate: '%s',
    canonical: canonical,
    noindex: true,
    nofollow: true,
    description: undefined,
    openGraph: {
      url: canonical,
      site_name: SITE?.name,
      images: [],
      locale: I18N?.language || 'en',
      type: 'website',
    },
    twitter: {
      cardType: (Array.isArray(processedOpenGraph?.images) && processedOpenGraph.images.length > 0) || (externalOgImageUrl ? 1 : 0) ? 'summary_large_image' : 'summary',
    },
  },
  {
    title: METADATA?.title?.default,
    titleTemplate: METADATA?.title?.template,
    noindex: typeof METADATA?.robots?.index !== 'undefined' ? !METADATA.robots.index : undefined,
    nofollow: typeof METADATA?.robots?.follow !== 'undefined' ? !METADATA.robots.follow : undefined,
    description: METADATA?.description,
    openGraph: METADATA?.openGraph,
    twitter: METADATA?.twitter,
  },
  {
    title: title,
    titleTemplate: ignoreTitleTemplate ? '%s' : undefined,
    canonical: canonical,
    noindex: (robots as MetaDataRobots)?.index === true ? false : undefined,
    nofollow: (robots as MetaDataRobots)?.follow === true ? false : undefined,
    description: description,
    openGraph: { url: canonical, ...processedOpenGraph },
    twitter: twitter,
  }
);

const finalOpenGraph = (Array.isArray(processedOpenGraph?.images) && processedOpenGraph.images.length > 0)
  ? await adaptOpenGraphImages(seoProps?.openGraph, Astro.site)
  : seoProps?.openGraph;

---

<head>
  {externalOgImageUrl && (
    <meta property="og:image" content={externalOgImageUrl} />
  )}

  <AstroSeo {...{ ...seoProps, openGraph: finalOpenGraph }} />
</head>
