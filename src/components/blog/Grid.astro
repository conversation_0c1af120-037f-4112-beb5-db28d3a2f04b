---
import Item from '~/components/blog/GridItem.astro';
import type { Post } from '~/types';
import { twMerge } from 'tailwind-merge';

export interface Props {
  posts: Array<Post>;
  gridClass?: string;
}

const { posts, gridClass } = Astro.props;

const defaultGridClass = 'grid gap-6 row-gap-5 md:grid-cols-2 lg:grid-cols-3 -mb-6';
const finalGridClass = twMerge(defaultGridClass, gridClass);
---

<div class={finalGridClass}>
  {posts.map((post) => <Item post={post} />)}
</div>
