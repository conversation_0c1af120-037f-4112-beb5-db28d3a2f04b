---
import { APP_BLOG } from 'astrowind:config';
import type { Post } from '~/types';

import Image from '~/components/common/Image.astro';
import { Icon } from 'astro-icon/components';

import { findImage } from '~/utils/images';
import { getPermalink } from '~/utils/permalinks';
import { getFormattedDate } from '~/utils/utils';

export interface Props {
  post: Post;
}

const { post } = Astro.props;
const image = await findImage(post.image);

const link = APP_BLOG?.post?.isEnabled ? getPermalink(post.permalink, 'post') : '';
const categoryLink = post.category && APP_BLOG?.category?.isEnabled ? getPermalink(post.category.slug, 'category') : '';
---

<article
  class="mb-6 transition intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade"
>
  <div class="relative bg-gray-400 dark:bg-slate-700 rounded shadow-lg mb-6 overflow-hidden h-0 pb-[56.25%]">
    {
      image &&
        (link ? (
          <a href={link}>
            <Image
              src={image}
              class="absolute inset-0 object-cover w-full h-full rounded shadow-lg bg-gray-400 dark:bg-slate-700 object-top"
              widths={[400, 900]}
              width={400}
              sizes="(max-width: 900px) 400px, 900px"
              alt={post.title}
              aspectRatio="16:9"
              layout="cover"
              loading="lazy"
              decoding="async"
            />
          </a>
        ) : (
          <Image
            src={image}
            class="absolute inset-0 object-cover w-full h-full rounded shadow-lg bg-gray-400 dark:bg-slate-700 object-top"
            widths={[400, 900]}
            width={400}
            sizes="(max-width: 900px) 400px, 900px"
            alt={post.title}
            aspectRatio="16:9"
            layout="cover"
            loading="lazy"
            decoding="async"
          />
        ))
    }
  </div>

  {post.category && (
    <div class="mb-1 text-xs font-medium text-primary-500 dark:text-primary-400 uppercase">
      {categoryLink ? (
        <a href={categoryLink} class="hover:underline">
          {post.category.title}
        </a>
      ) : (
        post.category.title
      )}
    </div>
  )}

  <h3 class="text-xl sm:text-2xl font-bold leading-tight mb-2 font-heading dark:text-slate-300">
    {
      link ? (
        <a class="inline-block hover:text-primary dark:hover:text-blue-700 transition ease-in duration-200" href={link}>
          {post.title}
        </a>
      ) : (
        post.title
      )
    }
  </h3>

  <p class="text-muted dark:text-slate-400 text-sm mb-2">{post.excerpt}</p>

  <div class="text-sm text-muted dark:text-slate-500 mt-2">
    {post.author && (
      <span class="inline-flex items-center">
        <Icon name="tabler:user" class="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
        {post.author}
      </span>
    )}
    {post.author && post.updateDate && <span class="mx-1">·</span>}
    {post.updateDate && (
      <span class="inline-flex items-center">
        <Icon name="tabler:calendar-event" class="w-4 h-4 mr-1 rtl:mr-0 rtl:ml-1" />
        <time datetime={String(post.updateDate)}>{getFormattedDate(post.updateDate)}</time>
      </span>
    )}
  </div>
</article>
