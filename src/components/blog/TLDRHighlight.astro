---
import Button from '~/components/ui/Button.astro';

interface Props {
  title?: string;
  pros: string[];
  cons: string[];
  prosTitle?: string;
  consTitle?: string;
  ratingValue?: number;
  ratingMax?: number;
  visitLinkHref?: string;
  visitLinkText?: string;
  visitLinkRel?: string;
  couponCode?: string;
  couponDescription?: string;
}

const {
  title,
  pros = [],
  cons = [],
  prosTitle = "Kelebihan",
  consTitle = "Kekurangan",
  ratingValue,
  ratingMax = 5,
  visitLinkHref,
  visitLinkText = "Kunjungi Situs",
  visitLinkRel,
  couponCode,
  couponDescription,
} = Astro.props;
---

<div class="tldr-nyc-style my-2 py-2">
  {title && <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">{title}</h3>}
  
  <div class="grid md:grid-cols-2 gap-x-6 gap-y-4">
    <div>
      {pros && pros.length > 0 && (
        <>
          <h4 class="text-base font-medium mb-2 text-gray-700 dark:text-gray-300 uppercase tracking-wider">{prosTitle}</h4>
          <ul class="space-y-1 list-disc list-inside pl-1 text-sm">
            {pros.map((pro) => (
              <li class="text-neutral-700 dark:text-neutral-400">
                {pro}
              </li>
            ))}
          </ul>
        </>
      )}
    </div>
    <div>
      {cons && cons.length > 0 && (
        <>
          <h4 class="text-base font-medium mb-2 text-gray-700 dark:text-gray-300 uppercase tracking-wider">{consTitle}</h4>
          <ul class="space-y-1 list-disc list-inside pl-1 text-sm">
            {cons.map((con) => (
              <li class="text-neutral-700 dark:text-neutral-400">
                {con}
              </li>
            ))}
          </ul>
        </>
      )}
    </div>
  </div>

  {(ratingValue !== undefined || visitLinkHref || couponCode) && (
    <div class="space-y-3 text-sm">
      {ratingValue !== undefined && (
        <div class="flex items-center">
          <span class="font-medium text-neutral-600 dark:text-neutral-400 mr-2">Rating:</span>
          <span class="font-semibold text-gray-800 dark:text-gray-200">{ratingValue} / {ratingMax}</span>
        </div>
      )}

      {visitLinkHref && (
        <div>
          <Button
            href={visitLinkHref}
            target="_blank"
            rel={visitLinkRel}
            variant="primary"
            icon="tabler:arrow-right"
          >
            {visitLinkText}
          </Button>
        </div>
      )}

      {couponCode && (
        <div>
          <span class="text-neutral-700 dark:text-neutral-400">
            {couponDescription || "Kode Kupon:"} <strong class="font-semibold text-gray-800 dark:text-gray-200">{couponCode}</strong>
          </span>
        </div>
      )}
    </div>
  )}
</div> 