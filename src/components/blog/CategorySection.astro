---
import { Icon } from 'astro-icon/components';
import BlogGrid from './BlogGrid.astro';
import type { Post } from '~/types';
import { getPermalink } from '~/utils/permalinks';

export interface Props {
  title: string;
  posts: Post[];
  category?: string;
  icon?: string;
  limit?: number;
}

const {
  title,
  posts = [],
  category,
  icon = 'tabler:category',
  limit = 3
} = Astro.props;

// Filter posts by category or use provided posts if no category
const filteredPosts = category
  ? posts.filter(post => post.category?.slug === category).slice(0, limit)
  : posts.slice(0, limit);

const categoryLink = category ? getPermalink(category, 'category') : getPermalink('', 'blog');
---

{filteredPosts.length > 0 && (
  <div class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold dark:text-white flex items-center">
        <Icon name={icon} class="w-6 h-6 text-primary mr-2" />
        {title}
      </h2>
      <a href={categoryLink} class="text-sm font-medium text-primary hover:underline flex items-center">
        Lihat Semua
        <Icon name="tabler:chevron-right" class="w-4 h-4 ml-1" />
      </a>
    </div>

    <BlogGrid
      posts={filteredPosts}
      postCardVariant={category === 'review-hosting' ? 'review' : category === 'panduan' ? 'guide-page' : 'regular'}
    />
  </div>
)}
