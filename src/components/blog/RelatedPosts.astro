---
import { APP_BLOG } from 'astrowind:config';

import { getRelatedPosts } from '~/utils/blog';
import BlogHighlightedPosts from '../widgets/BlogHighlightedPosts.astro';
import type { Post } from '~/types';

export interface Props {
  post: Post;
}

const { post } = Astro.props;

const relatedPosts = post.tags ? await getRelatedPosts(post, 9) : [];
---

{
  APP_BLOG.isRelatedPostsEnabled ? (
    <BlogHighlightedPosts
      classes={{
        container:
          'max-w-6xl pt-0 lg:pt-0 md:pt-0 intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade',
      }}
      title="Related Posts"
      postIds={relatedPosts.map((post) => post.id)}
      gridClass="lg:grid-cols-3"
    />
  ) : null
}
