---
import type { ImageMetadata } from 'astro';
import { Icon } from 'astro-icon/components';
import Image from '~/components/common/Image.astro';
import PostTags from '~/components/blog/Tags.astro';

import { APP_BLOG } from 'astrowind:config';
import type { Post } from '~/types';

import { getPermalink } from '~/utils/permalinks';
import { findImage } from '~/utils/images';
import { getFormattedDate } from '~/utils/utils';

export interface Props {
  post: Post;
}

const { post } = Astro.props;
const image = (await findImage(post.image)) as ImageMetadata | undefined;

const link = APP_BLOG?.post?.isEnabled ? getPermalink(post.permalink, 'post') : '';
---

<article
  class={`max-w-md mx-auto md:max-w-none grid gap-6 intersect-once intersect-quarter motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade mb-4 items-center ${image ? 'md:grid-cols-[240px_1fr]' : ''}`}
>
  {
    image &&
      (link ? (
        <a class="relative block group" href={link ?? 'javascript:void(0)'}>
          <div class="relative h-0 pb-[75%] overflow-hidden bg-gray-400 dark:bg-slate-700 rounded shadow-lg">
            {image && (
              <Image
                src={image}
                class="absolute inset-0 object-cover w-full h-full rounded shadow-lg bg-gray-400 dark:bg-slate-700"
                widths={[240, 480]}
                width={240}
                sizes="(max-width: 767px) 100vw, 240px"
                alt={post.title}
                aspectRatio="4:3" 
                loading="lazy"
                decoding="async"
              />
            )}
          </div>
        </a>
      ) : (
        <div class="relative h-0 pb-[75%] overflow-hidden bg-gray-400 dark:bg-slate-700 rounded shadow-lg">
          {image && (
            <Image
              src={image}
              class="absolute inset-0 object-cover w-full h-full rounded shadow-lg bg-gray-400 dark:bg-slate-700"
              widths={[240, 480]}
              width={240}
              sizes="(max-width: 767px) 100vw, 240px"
              alt={post.title}
              aspectRatio="4:3" 
              loading="lazy"
              decoding="async"
            />
          )}
        </div>
      ))
  }
  <div class="mt-2 md:mt-0">
    <header>
      <div class="mb-1">
        <span class="text-xs text-muted dark:text-slate-500">
          <Icon name="tabler:clock" class="w-3 h-3 inline-block -mt-0.5" />
          <time datetime={String(post.publishDate)} class="inline-block">{getFormattedDate(post.publishDate)}</time>
          {
            post.author && (
              <>
                <span class="mx-1">·</span>
                <Icon name="tabler:user" class="w-3 h-3 inline-block -mt-0.5" />
                <span>{post.author.replaceAll('-', ' ')}</span>
              </>
            )
          }
          {
            post.category && (
              <>
                <span class="mx-1">·</span>
                <a class="hover:underline" href={getPermalink(post.category.slug, 'category')}>
                  {post.category.title}
                </a>
              </>
            )
          }
        </span>
      </div>
      <h2 class="text-xl sm:text-2xl font-bold leading-tight mb-1 font-heading dark:text-slate-300">
        {
          link ? (
            <a
              class="inline-block hover:text-primary dark:hover:text-blue-700 transition ease-in duration-200"
              href={link}
            >
              {post.title}
            </a>
          ) : (
            post.title
          )
        }
      </h2>
    </header>

    {post.excerpt && <p class="flex-grow text-muted dark:text-slate-400 text-sm mt-1">{post.excerpt}</p>}
    {
      post.tags && Array.isArray(post.tags) ? (
        <footer class="mt-3">
          <PostTags tags={post.tags} class="text-xs" />
        </footer>
      ) : (
        <Fragment />
      )
    }
  </div>
</article>
