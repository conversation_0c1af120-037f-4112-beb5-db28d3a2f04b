---
import { APP_BLOG } from 'astrowind:config';
import type { Post } from '~/types';

import Image from '~/components/common/Image.astro';
import { Icon } from 'astro-icon/components';

import { findImage } from '~/utils/images';
import { getPermalink } from '~/utils/permalinks';
import { getFormattedDate } from '~/utils/utils';

// Extended MetaData interface to include additional properties
interface ExtendedMetaData {
  layout?: string;
  isGuidePage?: boolean;
  chapterCount?: string | number;
  readingTime?: number;
  rating?: string | number;
  featured?: boolean;
  [key: string]: any;
}

export interface Props {
  post: Post;
  variant?: 'regular' | 'guide' | 'guide-page' | 'review' | 'featured';
}

const { post, variant = 'regular' } = Astro.props;
const image = await findImage(post.image);

const link = APP_BLOG?.post?.isEnabled ? getPermalink(post.permalink, 'post') : '';
const categoryLink = post.category && APP_BLOG?.category?.isEnabled ? getPermalink(post.category.slug, 'category') : '';

// Cast metadata to ExtendedMetaData for type safety
const metadata = post.metadata as ExtendedMetaData;

// Determine post type based on metadata or category
const isGuidePage = metadata?.layout === 'guide-page' || metadata?.isGuidePage === true;
const isGuide = post.category?.slug === 'panduan' || post.tags?.some(tag => tag.slug === 'panduan');
const isReview = post.category?.slug === 'review-hosting';

// Use provided variant or determine automatically
const cardVariant = variant !== 'regular'
  ? variant
  : isGuidePage
    ? 'guide-page'
    : isGuide
      ? 'guide'
      : isReview
        ? 'review'
        : 'regular';

// Get chapter count for guides
const getChapterCount = () => {
  if (cardVariant === 'guide-page') {
    // Try to get chapter count from metadata
    if (metadata?.chapterCount) {
      return metadata.chapterCount.toString();
    }

    // Try to count from guideId
    if (metadata?.guideId) {
      // For panduan-web-hosting, we know it has 5 chapters
      if (metadata.guideId === 'web-hosting-guide') {
        return '5';
      }
      // For panduan-cpanel, we know it has 9 chapters
      else if (metadata.guideId === 'cpanel-guide') {
        return '9';
      }
    }

    // Try to count from title
    if (post.title) {
      if (post.title.includes('cPanel')) {
        return '9';
      } else if (post.title.includes('Web Hosting')) {
        return '5';
      }
    }

    // Default value if no chapter count is available
    return '5+';
  }
  return null;
};

const chapterCount = getChapterCount();

// Get reading time estimate
const readingTime = metadata?.readingTime || (post.excerpt?.length ? Math.ceil(post.excerpt.length / 1000) : 5);
---

{cardVariant === 'featured' ? (
  <article class="relative flex flex-col md:flex-row overflow-hidden rounded-xl shadow-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950 transition-all duration-200 hover:shadow-xl hover:-translate-y-1">
    <div class="md:w-2/5 relative">
      {image && (
        <a href={link} class="absolute inset-0">
          <Image
            src={image}
            class="w-full h-full object-cover"
            widths={[400, 900]}
            width={900}
            sizes="(max-width: 900px) 400px, 900px"
            alt={post.title}
            aspectRatio="16:9"
            loading="lazy"
            decoding="async"
          />
          {isGuidePage && (
            <div class="absolute inset-0 bg-primary/60 flex items-center justify-center">
              <div class="text-white text-center px-4">
                <div class="text-sm font-semibold uppercase tracking-wide mb-1">Panduan Lengkap</div>
                <div class="flex items-center justify-center gap-2">
                  <Icon name="tabler:book" class="w-5 h-5" />
                  <span>{chapterCount} Bab</span>
                </div>
              </div>
            </div>
          )}
        </a>
      )}
    </div>
    <div class="flex-1 p-6 md:p-8 flex flex-col">
      {post.category && (
        <div class="mb-3">
          <a
            href={categoryLink}
            class="text-xs font-semibold px-2 py-1 rounded-md bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-dark hover:bg-primary/20 transition-colors"
          >
            {post.category.title}
          </a>
        </div>
      )}
      <h2 class="text-2xl sm:text-3xl font-bold leading-tight mb-4 font-heading">
        {link ? (
          <a class="hover:text-primary dark:hover:text-blue-400 transition-colors" href={link}>
            {post.title}
          </a>
        ) : (
          post.title
        )}
      </h2>
      <p class="text-gray-700 dark:text-gray-400 flex-grow mb-5">{post.excerpt}</p>
      <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-auto">
        <div class="flex items-center mr-4">
          <Icon name="tabler:calendar" class="w-4 h-4 mr-1" />
          <time datetime={String(post.publishDate)}>{getFormattedDate(post.updateDate || post.publishDate)}</time>
        </div>
        <div class="flex items-center">
          <Icon name="tabler:clock" class="w-4 h-4 mr-1" />
          <span>{readingTime} menit</span>
        </div>
      </div>
    </div>
  </article>
) : cardVariant === 'guide-page' ? (
  <article class="relative flex flex-col overflow-hidden rounded-xl shadow-md border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950 transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
    {image && (
      <a href={link} class="relative block h-48 overflow-hidden">
        <Image
          src={image}
          class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
          widths={[400, 900]}
          width={400}
          sizes="400px"
          alt={post.title}
          aspectRatio="16:9"
          loading="lazy"
          decoding="async"
        />
        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
          <div class="p-4 text-white">
            <div class="flex items-center gap-2 mb-1">
              <Icon name="tabler:book" class="w-4 h-4" />
              <span class="text-sm font-medium">{chapterCount} Bab</span>
            </div>
            <div class="text-xs font-semibold uppercase tracking-wide">Panduan Lengkap</div>
          </div>
        </div>
      </a>
    )}
    <div class="p-5 flex-1 flex flex-col">
      <h3 class="text-xl font-bold leading-tight mb-3 font-heading">
        {link ? (
          <a class="hover:text-primary dark:hover:text-blue-400 transition-colors" href={link}>
            {post.title}
          </a>
        ) : (
          post.title
        )}
      </h3>
      <p class="text-gray-700 dark:text-gray-400 text-sm line-clamp-3 mb-4">{post.excerpt}</p>
      <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-auto">
        <div class="flex items-center mr-3">
          <Icon name="tabler:calendar" class="w-3.5 h-3.5 mr-1" />
          <time datetime={String(post.publishDate)}>{getFormattedDate(post.updateDate || post.publishDate)}</time>
        </div>
        <div class="flex items-center">
          <Icon name="tabler:clock" class="w-3.5 h-3.5 mr-1" />
          <span>{readingTime} menit</span>
        </div>
      </div>
    </div>
  </article>
) : cardVariant === 'review' ? (
  <article class="relative flex flex-col overflow-hidden rounded-xl shadow-md border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950 transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
    {image && (
      <a href={link} class="relative block h-48 overflow-hidden">
        <Image
          src={image}
          class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
          widths={[400, 900]}
          width={400}
          sizes="400px"
          alt={post.title}
          aspectRatio="16:9"
          loading="lazy"
          decoding="async"
        />
        <div class="absolute top-0 right-0 bg-yellow-500 text-white text-xs font-bold px-2 py-1 m-2 rounded">
          Review
        </div>
      </a>
    )}
    <div class="p-5 flex-1 flex flex-col">
      <h3 class="text-xl font-bold leading-tight mb-3 font-heading">
        {link ? (
          <a class="hover:text-primary dark:hover:text-blue-400 transition-colors" href={link}>
            {post.title}
          </a>
        ) : (
          post.title
        )}
      </h3>
      <p class="text-gray-700 dark:text-gray-400 text-sm line-clamp-3 mb-4">{post.excerpt}</p>
      <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-auto">
        <div class="flex items-center mr-3">
          <Icon name="tabler:calendar" class="w-3.5 h-3.5 mr-1" />
          <time datetime={String(post.publishDate)}>{getFormattedDate(post.updateDate || post.publishDate)}</time>
        </div>
        <div class="flex items-center">
          <Icon name="tabler:clock" class="w-3.5 h-3.5 mr-1" />
          <span>{readingTime} menit</span>
        </div>
      </div>
    </div>
  </article>
) : (
  <article class="relative flex flex-col overflow-hidden rounded-xl shadow-md border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-950 transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
    {image && (
      <a href={link} class="relative block h-48 overflow-hidden">
        <Image
          src={image}
          class="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
          widths={[400, 900]}
          width={400}
          sizes="400px"
          alt={post.title}
          aspectRatio="16:9"
          loading="lazy"
          decoding="async"
        />
        {post.category && (
          <div class="absolute top-0 left-0 bg-primary text-white text-xs font-bold px-2 py-1 m-2 rounded">
            {post.category.title}
          </div>
        )}
      </a>
    )}
    <div class="p-5 flex-1 flex flex-col">
      <h3 class="text-xl font-bold leading-tight mb-3 font-heading">
        {link ? (
          <a class="hover:text-primary dark:hover:text-blue-400 transition-colors" href={link}>
            {post.title}
          </a>
        ) : (
          post.title
        )}
      </h3>
      <p class="text-gray-700 dark:text-gray-400 text-sm line-clamp-3 mb-4">{post.excerpt}</p>
      <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-auto">
        <div class="flex items-center mr-3">
          <Icon name="tabler:calendar" class="w-3.5 h-3.5 mr-1" />
          <time datetime={String(post.publishDate)}>{getFormattedDate(post.updateDate || post.publishDate)}</time>
        </div>
        <div class="flex items-center">
          <Icon name="tabler:clock" class="w-3.5 h-3.5 mr-1" />
          <span>{readingTime} menit</span>
        </div>
      </div>
    </div>
  </article>
)}
