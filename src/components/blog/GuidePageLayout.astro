---
import { Icon } from 'astro-icon/components';
import Image from '~/components/common/Image.astro';
import SocialShare from '~/components/common/SocialShare.astro';
import FYINotice from '~/components/blog/FYINotice.astro';
import UpdateNotice from '~/components/blog/UpdateNotice.astro';
import { getPermalink } from '~/utils/permalinks';
import { getFormattedDate } from '~/utils/utils';
import type { Post } from '~/types';

export interface Props {
  post: Post;
  url: string | URL;
  noticeType?: 'fyi' | 'update' | null;
  noticeDate?: Date | string;
  showNotice?: boolean;
}

const {
  post,
  url,
  noticeType = null,
  noticeDate,
  showNotice = true,
} = Astro.props;

// Define chapter interface
interface Chapter {
  id: string;
  title: string;
}

// Extract chapters from the content using h2 headings
const extractChapters = (content: string): Chapter[] => {
  // First try to match h2 with id attribute
  const regexWithId = /<h2.*?id="(.*?)".*?>(.*?)<\/h2>/g;
  const chapters: Chapter[] = [];
  let match: RegExpExecArray | null;

  while ((match = regexWithId.exec(content)) !== null) {
    chapters.push({
      id: match[1],
      title: match[2].replace(/<.*?>/g, '') // Remove any HTML tags inside the heading
    });
  }

  // If no chapters found with id, try to match h2 without id and generate ids
  if (chapters.length === 0) {
    // Look for Markdown style headings or h2 tags without id
    const markdownHeadings = content.match(/## (.*?)(\n|$)/g) || [];
    const h2Headings = content.match(/<h2.*?>(.*?)<\/h2>/g) || [];

    // Process Markdown headings
    markdownHeadings.forEach((heading) => {
      const title = heading.replace(/^## /, '').trim();
      const id = title.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
      chapters.push({ id, title });
    });

    // Process h2 tags without id
    if (chapters.length === 0) {
      h2Headings.forEach((heading) => {
        const title = heading.replace(/<h2.*?>(.*?)<\/h2>/, '$1').replace(/<.*?>/g, '').trim();
        const id = title.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
        chapters.push({ id, title });
      });
    }

    // If still no chapters found, create manual chapters from the content
    if (chapters.length === 0) {
      // Look for text that starts with "Bab" followed by a number
      const babRegex = /Bab \d+: (.*?)(\n|$)/g;
      let babMatch;

      while ((babMatch = babRegex.exec(content)) !== null) {
        const title = babMatch[0].trim();
        const id = title.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
        chapters.push({ id, title });
      }
    }
  }

  return chapters;
};

// Get content as string to extract chapters
let contentString = '';
if (post.Content) {
  // Get content from slot
  const content = await Astro.slots.render('default');
  contentString = content;
}

const chapters: Chapter[] = extractChapters(contentString);

// Calculate estimated reading time (average reading speed: 200 words per minute)
const calculateReadingTime = (content: string) => {
  const words = content.trim().split(/\s+/).length;
  const readingTime = Math.ceil(words / 200);
  return readingTime;
};

const totalReadingTime = calculateReadingTime(contentString);

// Author information
const authorDescription = "Hosting Reviewer di PenasihatHosting.com.";
---

<section class="mx-auto">
  {/* Simplified Disclosure Bar - Wirecutter style */}
  <div class="max-w-global mx-auto px-4 sm:px-6 my-6 text-center">
    <p class="text-xs text-gray-600 dark:text-gray-400">
      Kami mereview hosting secara independen. Ketika Anda membeli hosting melalui link kami, kami mendapatkan komisi (10-50%), tanpa ada biaya tambahan untuk Anda.
    </p>
  </div>

  <div class="mx-auto px-4 sm:px-6 lg:px-8 pb-12 md:pb-20 lg:pb-24">
    <div class="max-w-global mx-auto">
      <div class="grid lg:grid-cols-4 gap-16">
        {/* Sidebar with Table of Contents - Visible on large screens */}
        <aside class="hidden lg:block lg:col-span-1">
          <div class="sticky top-24 overflow-y-auto max-h-[calc(100vh-120px)] pr-4">
            <div class="mb-4">
              <div class="flex items-center mb-2">
                <Icon name="tabler:book" class="w-5 h-5 mr-2 text-primary" />
                <h3 class="font-bold text-lg">Daftar Isi</h3>
              </div>
              <div class="h-1 w-12 bg-primary mb-4"></div>

              <div class="flex items-center mb-4 text-sm text-gray-600 dark:text-gray-400">
                <Icon name="tabler:clock" class="w-4 h-4 mr-1" />
                <span>Waktu baca: {totalReadingTime} menit</span>
              </div>

              <nav class="toc">
                <ul class="space-y-2 text-sm">
                  {chapters.map((chapter, index) => (
                    <li>
                      <a
                        href={`#${chapter.id}`}
                        class="flex items-start py-1 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors group relative pl-0 hover:pl-2"
                      >
                        <span class="flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 flex items-center justify-center mr-2 group-hover:bg-primary/10 group-hover:text-primary">
                          {index + 1}
                        </span>
                        <span class="leading-tight">{chapter.title}</span>
                      </a>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>

            {/* Social Share in Sidebar */}
            <div class="mt-8 border-t pt-6 dark:border-gray-800">
              <h4 class="text-sm font-semibold mb-3">Bagikan Panduan Ini</h4>
              <SocialShare url={url} text={post.title} class="flex gap-2" />
            </div>
          </div>
        </aside>

        {/* Main Content Area */}
        <div class="lg:col-span-3">
          {/* Hero Section */}
          <div class="mb-10">
            <div class="relative">
              {/* Category and Tags */}
              <div class="flex flex-wrap gap-2 mb-4">
                {post.category && (
                  <a
                    href={getPermalink(post.category.slug, 'category')}
                    class="inline-block bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-dark py-1 px-3 rounded-md text-xs font-medium"
                  >
                    {post.category.title}
                  </a>
                )}

                {/* Show the first tag next to category */}
                {post.tags && post.tags.length > 0 && (
                  <a
                    href={getPermalink(post.tags[0].slug, 'tag')}
                    class="inline-block bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 py-1 px-3 rounded-md text-xs font-medium"
                  >
                    {post.tags[0].title}
                  </a>
                )}
              </div>

              {/* Title and Excerpt */}
              <div class="border-t-8 border-primary dark:border-primary pt-4 relative">
                <h1 class="text-4xl md:text-5xl font-extrabold leading-tight tracking-tight font-heading mb-4 text-gray-900 dark:text-white">
                  {post.title}
                </h1>

                <p class="text-lg text-gray-700 dark:text-gray-300 mb-6 max-w-3xl">
                  {post.excerpt}
                </p>

                {/* Guide Metadata */}
                <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-600 dark:text-gray-400">
                  {/* Updated Date */}
                  <div class="flex items-center">
                    <Icon name="tabler:refresh" class="w-4 h-4 mr-1" />
                    <span>Diperbarui <span class="font-medium">{getFormattedDate(post.updateDate || post.publishDate)}</span></span>
                  </div>

                  {/* Reading Time */}
                  <div class="flex items-center">
                    <Icon name="tabler:clock" class="w-4 h-4 mr-1" />
                    <span>Waktu baca: {totalReadingTime} menit</span>
                  </div>

                  {/* Chapter Count */}
                  <div class="flex items-center">
                    <Icon name="tabler:book" class="w-4 h-4 mr-1" />
                    <span>{chapters.length} bab</span>
                  </div>
                </div>

                {/* Mobile Table of Contents Toggle */}
                <div class="lg:hidden mb-6">
                  <button
                    id="toc-toggle"
                    class="flex items-center justify-between w-full px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-md text-left"
                  >
                    <span class="font-medium">Daftar Isi</span>
                    <Icon name="tabler:chevron-down" id="toc-icon" class="w-5 h-5 transition-transform" />
                  </button>
                  <div id="mobile-toc" class="hidden mt-2 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-md">
                    <ul class="space-y-2 text-sm">
                      {chapters.map((chapter, index) => (
                        <li>
                          <a
                            href={`#${chapter.id}`}
                            class="flex items-start py-1 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors relative pl-0 hover:pl-2"
                          >
                            <span class="flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 flex items-center justify-center mr-2 group-hover:bg-primary/10 group-hover:text-primary">
                              {index + 1}
                            </span>
                            <span class="leading-tight">{chapter.title}</span>
                          </a>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Featured Image */}
            {post.image ? (
              <div class="mb-8">
                <Image
                  src={post.image}
                  class="w-full rounded-lg shadow-lg"
                  widths={[400, 900, 1075]}
                  sizes="(min-width: 1024px) 65vw, 95vw"
                  alt={post?.excerpt || post.title || ''}
                  width={900}
                  height={506}
                  loading="eager"
                  decoding="async"
                  aspectRatio={16/9}
                />
              </div>
            ) : null}

            {/* Notice (FYI or Update) - Only shown if showNotice is true */}
            {showNotice && noticeType === 'fyi' && <FYINotice />}
            {showNotice && noticeType === 'update' && <UpdateNotice date={noticeDate} />}
          </div>

          {/* Main Content */}
          <div class="prose prose-base dark:prose-invert max-w-none" id="guide-content">
            <slot />
          </div>

          {/* Author Bio */}
          <div class="mt-12 pt-8 border-t dark:border-gray-800">
            <div class="flex flex-col sm:flex-row items-center sm:items-start gap-4">
              <div>
                <h3 class="text-lg font-semibold mb-1">Willya Randika</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">{authorDescription}</p>
                <div class="flex gap-2">
                  <a href="https://www.linkedin.com/in/randipalguna/" class="text-gray-500 hover:text-primary" aria-label="LinkedIn">
                    <Icon name="tabler:brand-linkedin" class="w-5 h-5" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  {/* Progress Bar */}
  <div id="reading-progress-container" class="fixed top-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-800 z-50">
    <div id="reading-progress" class="h-full bg-primary dark:bg-primary-dark w-0 transition-width duration-200"></div>
  </div>

  {/* Back to Top Button */}
  <button
    id="back-to-top"
    class="fixed bottom-6 right-6 bg-primary dark:bg-primary-dark text-white p-2 rounded-full shadow-lg opacity-0 invisible transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-xl z-40"
    aria-label="Back to top"
  >
    <Icon name="tabler:arrow-up" class="w-5 h-5" />
  </button>
</section>

<script>
  // Mobile TOC toggle
  const tocToggle = document.getElementById('toc-toggle');
  const mobileToc = document.getElementById('mobile-toc');
  const tocIcon = document.getElementById('toc-icon');

  if (tocToggle && mobileToc && tocIcon) {
    tocToggle.addEventListener('click', () => {
      mobileToc.classList.toggle('hidden');
      tocIcon.classList.toggle('rotate-180');
    });
  }

  // Reading progress
  const progressBar = document.getElementById('reading-progress');
  const content = document.querySelector('.prose');

  if (progressBar && content) {
    window.addEventListener('scroll', () => {
      const contentBox = content.getBoundingClientRect();
      const contentStart = contentBox.top < 0 ? Math.abs(contentBox.top) : 0;
      const contentHeight = contentBox.height;
      const windowHeight = window.innerHeight;
      const scrolled = contentStart / (contentHeight - windowHeight);

      progressBar.style.width = `${Math.min(scrolled * 100, 100)}%`;
    });
  }

  // Back to top button
  const backToTopButton = document.getElementById('back-to-top');

  if (backToTopButton) {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 300) {
        backToTopButton.classList.remove('opacity-0', 'invisible');
        backToTopButton.classList.add('opacity-100', 'visible');
      } else {
        backToTopButton.classList.remove('opacity-100', 'visible');
        backToTopButton.classList.add('opacity-0', 'invisible');
      }
    });

    backToTopButton.addEventListener('click', () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }

  // Function to update TOC and highlight current section
  const updateTOC = () => {
    const tocLinks = document.querySelectorAll('.toc a');
    const sections = Array.from(document.querySelectorAll('h2[id]')).map(heading => ({
      id: heading.id,
      element: heading,
      offsetTop: (heading as HTMLElement).offsetTop
    }));

    if (tocLinks.length > 0 && sections.length > 0) {
      // Set up scroll event listener
      window.addEventListener('scroll', () => {
        const scrollPosition = window.scrollY + 100; // Add offset for header

        // Find the current section
        let currentSection = sections[0].id;
        for (const section of sections) {
          if (scrollPosition >= section.offsetTop) {
            currentSection = section.id;
          } else {
            break;
          }
        }

        // Update active state in TOC
        tocLinks.forEach(link => {
          const href = link.getAttribute('href');
          if (href === `#${currentSection}`) {
            link.classList.add('text-primary', 'font-medium');
            link.querySelector('span:first-child')?.classList.add('bg-primary/10', 'text-primary');
          } else {
            link.classList.remove('text-primary', 'font-medium');
            link.querySelector('span:first-child')?.classList.remove('bg-primary/10', 'text-primary');
          }
        });
      });
    }
  };

  // Call updateTOC initially
  updateTOC();

  // Add id attributes to h2 elements that don't have them
  const guideContent = document.getElementById('guide-content');
  if (guideContent) {
    const h2Elements = guideContent.querySelectorAll('h2:not([id])');
    let idsAdded = false;

    h2Elements.forEach((h2) => {
      const title = h2.textContent || '';
      const id = title.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
      h2.setAttribute('id', id);
      idsAdded = true;
    });

    // If IDs were added, update TOC again
    if (idsAdded) {
      // Wait a bit for the DOM to update
      setTimeout(updateTOC, 100);
    }
  }

  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();

      const targetId = this.getAttribute('href');
      if (targetId === '#') return;

      const targetElement = document.querySelector(targetId);
      if (targetElement) {
        window.scrollTo({
          top: (targetElement as HTMLElement).offsetTop - 80, // Offset for fixed header
          behavior: 'smooth'
        });

        // Update URL without scrolling
        history.pushState('', '', targetId);
      }
    });
  });

  // This section has been moved up and integrated with the TOC update logic
</script>
