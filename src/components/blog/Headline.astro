---
const { title = await Astro.slots.render('default'), subtitle = await Astro.slots.render('subtitle') } = Astro.props;
---

<header class="mb-8 md:mb-16 max-w-3xl mx-auto">
  <div class="border-t-4 border-primary dark:border-blue-500 pt-6 text-center mx-auto" style="max-width: 70%;">
    <h1 class="text-4xl md:text-5xl font-bold leading-tighter tracking-tighter font-heading" set:html={title} />
    {
      subtitle && (
        <div class="mt-2 md:mt-3 mx-auto text-base text-gray-600 dark:text-slate-400 font-medium" set:html={subtitle} />
      )
    }
  </div>
</header>
