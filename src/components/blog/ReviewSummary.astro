---
// ReviewSummary component - A clean, professional summary box for review articles

interface Metric {
  name: string;
  value: string;
  icon?: 'green' | 'yellow' | 'red'; // Made icon optional
  note?: string; // Optional explanatory note
}

interface Props {
  title?: string;
  metrics: Metric[];
  rating?: number; // e.g., 3.5 out of 5
  className?: string;
}

const { 
  title = "TL;DR", 
  metrics = [],
  rating,
  className = ""
} = Astro.props;

// Helper function to render appropriate colored circle icon
const getIcon = (color: 'green' | 'yellow' | 'red') => {
  const baseClass = "inline-block w-4 h-4 rounded-full mr-2";
  
  if (color === 'green') return `<span class="${baseClass} bg-green-500"></span>`;
  if (color === 'yellow') return `<span class="${baseClass} bg-yellow-400"></span>`;
  if (color === 'red') return `<span class="${baseClass} bg-red-500"></span>`;
  
  return '';
};

// Function to render star rating
const renderRating = (rating: number) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  return Array(fullStars).fill('★').concat(
    hasHalfStar ? ['½'] : [],
    Array(emptyStars).fill('☆')
  ).join('');
};
---

<div class={`mb-8 ${className}`}>
  <h3 class="font-bold mb-4">{title}</h3>
  
  <div class="space-y-3">
    {metrics.map(metric => (
      <div class="flex flex-col">
        <div class="flex items-center font-medium text-base">
          {/* Only render icon if it exists */}
          {metric.icon && <Fragment set:html={getIcon(metric.icon)} />}
          <span class="font-bold mr-2">{metric.name}:</span>
          <span>{metric.value}</span>
        </div>
        {metric.note && <p class="text-gray-600 dark:text-gray-400 text-sm ml-6">{metric.note}</p>}
      </div>
    ))}
    
    {rating && (
      <div class="flex items-center justify-between mt-5 pt-3 border-t border-gray-200 dark:border-gray-700">
        <span class="font-bold">Rating:</span>
        <div>
          <span class="text-lg text-yellow-500 font-bold">{rating}</span> / 5 
          <span class="text-yellow-500 ml-2">{renderRating(rating)}</span>
        </div>
      </div>
    )}
    
    <slot /> {/* For any additional content */}
  </div>
</div> 