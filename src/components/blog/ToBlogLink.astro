---
import { Icon } from 'astro-icon/components';
import { getBlogPermalink } from '~/utils/permalinks';
import { I18N } from 'astrowind:config';
import Button from '~/components/ui/Button.astro';

const { textDirection } = I18N;
---

<div class="py-0">
  <Button variant="tertiary" class="px-0" href={getBlogPermalink()}>
    {
      textDirection === 'rtl' ? (
        <Icon name="tabler:chevron-right" class="w-5 h-5 mr-1" />
      ) : (
        <Icon name="tabler:chevron-left" class="w-5 h-5 mr-1" />
      )
    } Back to Blog
  </Button>
</div>
