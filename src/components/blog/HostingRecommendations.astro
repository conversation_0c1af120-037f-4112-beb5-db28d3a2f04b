---
import { Icon } from 'astro-icon/components';
import type { ImageMetadata } from 'astro';

// Import images directly
import idcloudhostLogo from '~/assets/images/providers/idcloudhost-logo.png';
import kencengLogo from '~/assets/images/providers/kenceng-logo.jpg';
import cloudwaysLogo from '~/assets/images/providers/cloudways-logo.svg';

// Define interface for hosting recommendation items
interface HostingItem {
  logo: ImageMetadata; // Use ImageMetadata type from Astro
  name: string;
  description: string;
  discount?: {
    amount: string;
    code: string;
  };
  url: string;
  isSponsored?: boolean;
  badge?: string;
}

// Default hosting recommendations, modify as needed
const defaultHosting: HostingItem[] = [
  {
    logo: idcloudhostLogo, // Use imported image object
    name: "IdCloudHost",
    description: "Provider hosting dengan uptime dan speed terbaik.",
    discount: {
      amount: "40%",
      code: "PENASIHATHOSTING"
    },
    url: "https://penasihathosting.com/go/idcloudhost",
    isSponsored: false,
    badge: "Hosting Terbaik"
  },
  {
    logo: kenceng<PERSON>ogo, // Use imported image object
    name: "Kenceng Solusindo",
    description: "Alternatif hosting terbaik dengan uptime dan speed yang juga bisa diandalkan.",
    url: "https://penasihathosting.com/go/kencengsolusindo",
    isSponsored: false,
    badge: "Pilihan Alternatif"
  },
  {
    logo: cloudwaysLogo, // Use imported image object
    name: "CloudWays",
    description: "Solusi VPS cloud hosting yang powerful namun tetap mudah digunakan.",
    discount: {
      amount: "30%",
      code: "PHCW30"
    },
    url: "https://penasihathosting.com/go/cloudways",
    isSponsored: true,
    badge: "Sponsored"
  }
];

interface Props {
  hosting?: HostingItem[];
}

const { hosting = defaultHosting } = Astro.props;
---

<div>
  <h3 class="text-[16px] font-bold mb-2">Rekomendasi Hosting Murah:</h3>
  
  <div class="space-y-3">
    {hosting.filter(item => !item.isSponsored).map((item) => (
      <a 
        href={item.url}
        target="_blank"
        rel="nofollow noopener noreferrer"
        class="bg-white border border-neutral-200 dark:border-neutral-700 rounded-lg overflow-hidden shadow-sm flex group hover:shadow-md transition-shadow"
      >
        <div class="p-3 pl-4 flex-grow">
          <div class="flex items-center">
            <div class="w-[60px] flex-shrink-0 mr-3">
              <img src={item.logo.src} alt={item.name} class="max-w-full max-h-[60px] object-contain" />
            </div>
            <div>
              <h4 class="text-[18px] font-bold mb-1 group-hover:text-accent transition-colors">{item.name}</h4>
              <p class="text-neutral-700 dark:text-neutral-300 text-[14px]">{item.description}</p>
              
              {item.discount && (
                <div class="mt-1">
                  <p>
                    <span class="text-[14px] text-red-600 font-bold inline-flex items-center">
                      🔥 Hemat {item.discount.amount} 🔥
                    </span>
                  </p>
                  <p class="text-[14px] text-neutral-700 dark:text-neutral-300">
                    kode: {item.discount.code}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div class="w-10 bg-accent flex items-center justify-center group-hover:bg-accent/90 transition-colors">
          <Icon name="tabler:chevron-right" class="w-6 h-6 text-white" />
        </div>
      </a>
    ))}
  </div>
  
  {hosting.some(item => item.isSponsored) && (
    <div class="mt-6">
      <h4 class="text-neutral-600 dark:text-neutral-400 text-[15px] font-bold mb-3">Sponsored:</h4>
      
      <div class="space-y-3">
        {hosting.filter(item => item.isSponsored).map(item => (
          <a 
            href={item.url}
            target="_blank"
            rel="nofollow noopener noreferrer"
            class="bg-white border border-neutral-200 dark:border-neutral-700 rounded-lg overflow-hidden shadow-sm flex group hover:shadow-md transition-shadow"
          >
            <div class="p-3 pl-4 flex-grow">
              <div class="flex items-center">
                <div class="w-[60px] flex-shrink-0 mr-3">
                  <img src={item.logo.src} alt={item.name} class="max-w-full max-h-[60px] object-contain" />
                </div>
                <div>
                  <h4 class="text-[18px] font-bold mb-1 group-hover:text-accent transition-colors">{item.name}</h4>
                  <p class="text-neutral-700 dark:text-neutral-300 text-[14px]">{item.description}</p>
                  
                  {item.discount && (
                    <div class="mt-1">
                      <p>
                        <span class="text-[14px] text-red-600 font-bold inline-flex items-center">
                          🔥 Hemat {item.discount.amount} 🔥
                        </span>
                      </p>
                      <p class="text-[14px] text-neutral-700 dark:text-neutral-300">
                        kode: {item.discount.code}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            <div class="w-10 bg-accent flex items-center justify-center group-hover:bg-accent/90 transition-colors">
              <Icon name="tabler:chevron-right" class="w-6 h-6 text-white" />
            </div>
          </a>
        ))}
      </div>
    </div>
  )}
</div> 