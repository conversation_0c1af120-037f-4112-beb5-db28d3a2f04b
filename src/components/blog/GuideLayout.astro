---
import { Icon } from 'astro-icon/components';
import Image from '~/components/common/Image.astro';
import SocialShare from '~/components/common/SocialShare.astro';
import FYINotice from '~/components/blog/FYINotice.astro';
import UpdateNotice from '~/components/blog/UpdateNotice.astro';
import { getPermalink } from '~/utils/permalinks';
import { getFormattedDate } from '~/utils/utils';
import type { Post } from '~/types';

export interface Props {
  post: Post;
  url: string | URL;
  noticeType?: 'fyi' | 'update' | null;
  noticeDate?: Date | string;
  chapters?: Array<{
    title: string;
    url: string;
    isActive?: boolean;
  }>;
  currentChapter?: number;
  totalChapters?: number;
}

const {
  post,
  url,
  noticeType = null,
  noticeDate,
  chapters = [],
  currentChapter = 1,
  totalChapters = 1
} = Astro.props;

const authorDescription = "Hosting Reviewer di PenasihatHosting.com.";
const hasChapters = chapters.length > 0 || totalChapters > 1;
const showChapterNavigation = hasChapters && totalChapters > 1;
const prevChapter = currentChapter > 1 ? chapters[currentChapter - 2] : null;
const nextChapter = currentChapter < totalChapters ? chapters[currentChapter] : null;
---

<section class="mx-auto">
  {/* Simplified Disclosure Bar - Wirecutter style */}
  <div class="max-w-global mx-auto px-4 sm:px-6 my-6 text-center">
    <p class="text-xs text-gray-600 dark:text-gray-400">
      Kami mereview hosting secara independen. Ketika Anda membeli hosting melalui link kami, kami mendapatkan komisi (10-50%), tanpa ada biaya tambahan untuk Anda.
    </p>
  </div>

  <article>
    {/* Content Section - Two Columns */}
    <div class="lg:grid lg:grid-cols-3 lg:gap-20 max-w-global mx-auto px-4 sm:px-6">
      {/* Main Content - 2/3 Width */}
      <div class="lg:col-span-2">
        {/* Title Section - With Wirecutter style thick top border */}
        <header class="mb-8 relative">
          <div class="text-[14px] font-medium text-neutral-500 hover:underline uppercase flex items-center gap-2 mb-4">
            {post.category && (
              <a
                href={getPermalink(post.category.slug, 'category')}
              >
                {post.category.title}
              </a>
            )}

            {/* Right arrow separator */}
            {post.category && post.tags && post.tags.length > 0 && (
              <Icon name="tabler:chevron-right" class="w-4 h-4 text-neutral-400" />
            )}

            {/* Show the first tag next to category */}
            {post.tags && post.tags.length > 0 && (
              <a
                href={getPermalink(post.tags[0].slug, 'tag')}
              >
                {post.tags[0].title}
              </a>
            )}
          </div>
          <div class="border-t-8 border-primary dark:border-primary pt-2 relative">
            <h1 class="text-4xl md:text-5xl font-extrabold leading-tight tracking-tight font-heading mb-3 text-gray-900 dark:text-white">
              {post.title}
            </h1>

            <p class="text-base text-gray-600 dark:text-gray-300 mb-4">
              {post.excerpt}
            </p>

            {/* Updated Date - Below title */}
            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-4">
              <span>Updated <span class="font-medium">{getFormattedDate(post.updateDate || post.publishDate)}</span></span>
            </div>

            {/* Social Share - Below updated date */}
            <SocialShare url={url} text={post.title} class="flex gap-2 mb-6" />
          </div>
        </header>

        {/* Featured Image */}
        {post.image ? (
          <div class="mb-8">
            <Image
              src={post.image}
              class="w-full sm:rounded-md shadow-lg"
              widths={[400, 900, 1075]}
              sizes="(min-width: 1024px) 65vw, 95vw"
              alt={post?.excerpt || post.title || ''}
              width={900}
              height={506}
              loading="lazy"
              decoding="async"
              aspectRatio={3/2}
            />
          </div>
        ) : null}

        {/* Author Card */}
        <div class="mb-6">
          {post.author && (
            <div class="flex items-center">
              <div class="w-12 h-12 rounded-full bg-gradient-to-br from-primary to-secondary dark:from-primary dark:to-secondary flex items-center justify-center text-white font-bold text-lg mr-4">
                {post.author.charAt(0).toUpperCase()}
              </div>
              <div>
                <span class="block text-base font-medium text-gray-900 dark:text-white">{post.author}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">{authorDescription}</span>
              </div>
            </div>
          )}
        </div>

        {/* Chapter indicator if this is part of a multi-chapter guide */}
        {hasChapters && (
          <div class="mb-8 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-primary">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-primary dark:text-blue-300">
                  Panduan Lengkap
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-300">
                  Bab {currentChapter} dari {totalChapters}
                </p>
              </div>
              <a href="#guide-chapters" class="text-sm font-medium text-primary hover:text-blue-700 dark:text-blue-300 dark:hover:text-blue-200 flex items-center">
                Lihat Semua Bab
                <Icon name="tabler:chevron-down" class="w-4 h-4 ml-1" />
              </a>
            </div>
          </div>
        )}

        <div class="prose prose-base dark:prose-invert dark:prose-headings:text-neutral-200 prose-headings:font-heading prose-headings:leading-tight prose-headings:tracking-tight prose-headings:font-bold prose-a:font-bold prose-a:text-[var(--aw-color-accent)] prose-a:hover:text-[var(--aw-color-accent-hover)] dark:prose-a:text-[var(--aw-color-accent)] dark:prose-a:hover:text-[var(--aw-color-accent-hover)] prose-img:rounded-md prose-headings:scroll-mt-[80px] max-w-none leading-normal">
          <slot />
        </div>

        {/* Chapter Navigation */}
        {showChapterNavigation && (
          <div class="my-12 border-t border-gray-200 dark:border-gray-800 pt-8">
            <h3 class="text-xl font-bold mb-6 text-gray-900 dark:text-white">Navigasi Panduan</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              {prevChapter && (
                <a href={prevChapter.url} class="flex items-start p-4 border border-gray-200 dark:border-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                  <div class="mr-4 text-primary">
                    <Icon name="tabler:arrow-left" class="w-5 h-5" />
                  </div>
                  <div>
                    <span class="block text-sm text-gray-500 dark:text-gray-400">Bab Sebelumnya</span>
                    <span class="font-medium text-gray-900 dark:text-white">{prevChapter.title}</span>
                  </div>
                </a>
              )}

              {nextChapter && (
                <a href={nextChapter.url} class="flex items-start p-4 border border-gray-200 dark:border-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                  <div class="mr-4 text-primary">
                    <Icon name="tabler:arrow-right" class="w-5 h-5" />
                  </div>
                  <div>
                    <span class="block text-sm text-gray-500 dark:text-gray-400">Bab Selanjutnya</span>
                    <span class="font-medium text-gray-900 dark:text-white">{nextChapter.title}</span>
                  </div>
                </a>
              )}
            </div>
          </div>
        )}

        {/* All Chapters List */}
        {hasChapters && (
          <div id="guide-chapters" class="my-12 border-t border-gray-200 dark:border-gray-800 pt-8">
            <h3 class="text-xl font-bold mb-6 text-gray-900 dark:text-white">Semua Bab Panduan</h3>
            <div class="space-y-4">
              {chapters.map((chapter, index) => (
                <a
                  href={chapter.url}
                  class:list={[
                    "flex items-center p-4 border rounded-lg transition-colors",
                    index + 1 === currentChapter
                      ? "bg-blue-50 dark:bg-blue-900/20 border-primary dark:border-blue-700"
                      : "border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50"
                  ]}
                >
                  <div class:list={[
                    "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-4",
                    index + 1 === currentChapter
                      ? "bg-primary text-white"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                  ]}>
                    {index + 1}
                  </div>
                  <div>
                    <span class:list={[
                      "font-medium",
                      index + 1 === currentChapter
                        ? "text-primary dark:text-blue-300"
                        : "text-gray-900 dark:text-white"
                    ]}>
                      {chapter.title}
                    </span>
                  </div>
                  {index + 1 === currentChapter && (
                    <div class="ml-auto">
                      <span class="text-xs font-medium px-2 py-1 bg-primary/10 text-primary dark:bg-blue-900/30 dark:text-blue-300 rounded-full">
                        Sedang Dibaca
                      </span>
                    </div>
                  )}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Sidebar */}
      <div class="lg:col-span-1">
        {/* Static content first */}
        

        {/* Sticky recommendations section */}
        <div
          style="position: -webkit-sticky; position: sticky; top: 6rem; z-index: 10;"
        >
          <div class="mt-0 lg:mt-0 mb-10">
          {noticeType === 'fyi' && <FYINotice date={noticeDate} />}
          {noticeType === 'update' && <UpdateNotice date={noticeDate} />}

          {/* Chapter List in Sidebar */}
          {hasChapters && (
            <div class="bg-neutral-100 dark:bg-neutral-800 p-6 rounded-md mb-6">
              <h3 class="text-sm font-bold mb-4 dark:text-neutral-200">DAFTAR BAB</h3>
              <div class="space-y-3">
                {chapters.map((chapter, index) => (
                  <div class:list={[
                    "flex items-center",
                    index + 1 === currentChapter ? "text-primary font-medium" : "text-gray-700 dark:text-gray-300"
                  ]}>
                    <span class:list={[
                      "w-6 h-6 rounded-full flex items-center justify-center text-xs mr-2",
                      index + 1 === currentChapter
                        ? "bg-primary text-white"
                        : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                    ]}>
                      {index + 1}
                    </span>
                    <a
                      href={chapter.url}
                      class:list={[
                        "text-sm hover:underline",
                        index + 1 === currentChapter ? "text-primary dark:text-blue-300" : ""
                      ]}
                    >
                      {chapter.title}
                    </a>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  </article>
</section>

<style>
  /* Target li elements specifically within the prose content area of this layout */
  div.prose :global(li) {
    margin-top: 0;
    margin-bottom: 0;
  }
</style>
