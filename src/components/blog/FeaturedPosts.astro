---
import { Icon } from 'astro-icon/components';
import PostCard from './PostCard.astro';
import type { Post } from '~/types';

export interface Props {
  title?: string;
  posts: Post[];
  highlightFirst?: boolean;
}

const { 
  title = 'Artikel Unggulan', 
  posts = [],
  highlightFirst = true
} = Astro.props;

// Get the first post for highlight if enabled and posts exist
const highlightedPost = highlightFirst && posts.length > 0 ? posts[0] : null;
// Get remaining posts
const remainingPosts = highlightFirst && posts.length > 0 ? posts.slice(1) : posts;
---

{posts.length > 0 && (
  <div class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold dark:text-white flex items-center">
        <Icon name="tabler:star" class="w-6 h-6 text-primary mr-2" />
        {title}
      </h2>
      <a href="/blog" class="text-sm font-medium text-primary hover:underline flex items-center">
        <PERSON>hat Semua
        <Icon name="tabler:chevron-right" class="w-4 h-4 ml-1" />
      </a>
    </div>

    <div class="grid gap-8">
      {highlightedPost && (
        <PostCard post={highlightedPost} variant="featured" />
      )}
      
      {remainingPosts.length > 0 && (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {remainingPosts.map(post => (
            <PostCard 
              post={post} 
              variant={
                post.metadata?.layout === 'guide-page' || post.metadata?.isGuidePage ? 'guide-page' :
                post.category?.slug === 'review-hosting' ? 'review' : 'regular'
              }
            />
          ))}
        </div>
      )}
    </div>
  </div>
)}
