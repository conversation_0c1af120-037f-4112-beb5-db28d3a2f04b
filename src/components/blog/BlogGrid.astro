---
import PostCard from '~/components/blog/PostCard.astro';
import type { Post } from '~/types';
import { twMerge } from 'tailwind-merge';

export interface Props {
  posts: Array<Post>;
  title?: string;
  postCardVariant?: 'regular' | 'guide' | 'guide-page' | 'review' | 'featured';
  layout?: 'grid' | 'list' | 'featured';
  className?: string;
}

const {
  posts,
  title,
  postCardVariant,
  layout = 'grid',
  className = '',
} = Astro.props;

// Note: These groupings can be used for future enhancements
// Currently not used but kept for reference
// const guidePosts = posts.filter(post =>
//   post.metadata?.layout === 'guide-page' ||
//   post.metadata?.isGuidePage === true
// );
//
// const reviewPosts = posts.filter(post =>
//   post.category?.slug === 'review-hosting'
// );
//
// const regularPosts = posts.filter(post =>
//   !(post.metadata?.layout === 'guide-page' ||
//     post.metadata?.isGuidePage === true) &&
//   !(post.category?.slug === 'review-hosting')
// );

// Determine layout classes
const getLayoutClasses = () => {
  switch (layout) {
    case 'list':
      return 'grid gap-8 md:gap-12';
    case 'featured':
      return 'grid gap-8';
    default:
      return 'grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
  }
};

const gridClass = twMerge(getLayoutClasses(), className);
---

{title && (
  <h2 class="text-2xl font-bold mb-6 border-l-4 border-primary pl-3 dark:text-white">{title}</h2>
)}

<div class={gridClass}>
  {layout === 'featured' && posts.length > 0 && (
    <div class="col-span-full mb-8">
      <PostCard post={posts[0]} variant="featured" />
    </div>
  )}

  {layout === 'featured' ? (
    <div class="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {posts.slice(1).map((post) => (
        <PostCard
          post={post}
          variant={postCardVariant || (
            post.metadata?.layout === 'guide-page' || post.metadata?.isGuidePage ? 'guide-page' :
            post.category?.slug === 'review-hosting' ? 'review' : 'regular'
          )}
        />
      ))}
    </div>
  ) : (
    posts.map((post) => (
      <PostCard
        post={post}
        variant={postCardVariant || (
          post.metadata?.layout === 'guide-page' || post.metadata?.isGuidePage ? 'guide-page' :
          post.category?.slug === 'review-hosting' ? 'review' : 'regular'
        )}
      />
    ))
  )}
</div>
