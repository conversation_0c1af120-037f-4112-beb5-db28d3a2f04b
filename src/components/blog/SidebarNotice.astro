---
interface Props {
  title?: string;
  date?: Date | string;
  classes?: string;
}

const { 
  title = "FYI", 
  date,
  classes = "mb-6"
} = Astro.props;
---

<div class={classes}>
  <h3 class="text-xl font-bold mb-2 dark:text-neutral-200">{title}</h3>
  <div class="text-neutral-700 dark:text-neutral-300 text-sm">
    <slot />
  </div>
  {date && (
    <div class="mt-3 text-xs text-neutral-500 dark:text-neutral-400">
      {typeof date === 'string' ? date : date.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' })}
    </div>
  )}
</div> 