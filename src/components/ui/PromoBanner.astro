---
interface Props {
  title: string;
  description: string;
  ctaText: string;
  ctaLink: string;
  promoCode: string;
  imageSrc: string;
  imageAlt: string;
  disclaimer?: string;
}

const { 
  title, 
  description, 
  ctaText, 
  ctaLink, 
  promoCode, 
  imageSrc, 
  imageAlt, 
  disclaimer 
} = Astro.props;
---

<div class="promo-banner">
  <div class="promo-content">
    <h3 class="promo-title">{title}</h3>
    <p class="promo-description">{description}</p>
    
    {promoCode && (
      <div class="promo-code">
        <strong>Kode Promo:</strong> {promoCode}
      </div>
    )}
    
    <a href={ctaLink} class="cta-button">
      {ctaText}
    </a>
    
    {disclaimer && (
      <p class="promo-disclaimer">
        <small>{disclaimer}</small>
      </p>
    )}
  </div>
  
  <div class="promo-image">
    <img 
      src={imageSrc} 
      alt={imageAlt}
      loading="lazy"
      width="400"
      height="300"
    />
  </div>
</div>

<style>
  .promo-banner {
    display: flex;
    flex-wrap: wrap;
    border: 3px solid rgb(1 97 239);
    border-radius: 12px;
    overflow: hidden;
    margin: 2rem 0;
    background: var(--aw-color-bg-page);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .promo-content {
    flex: 1;
    min-width: 250px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .promo-title {
    font-size: 1.5rem;
    color: var(--aw-color-text-heading);
    margin-bottom: 1rem;
  }
  
  .promo-description {
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
  
  .promo-code {
    background: color-mix(in srgb, var(--aw-color-primary) 10%, transparent);
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    border-left: 4px solid var(--aw-color-primary);
    color: var(--aw-color-text-default);
  }
  
  .cta-button {
    display: inline-block;
    background: var(--aw-color-primary);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: background-color 0.2s, opacity 0.2s;
    margin-bottom: 1rem;
  }
  
  .cta-button:hover {
    background: var(--aw-color-accent);
    opacity: 0.9;
  }
  
  .promo-disclaimer {
    font-size: 0.875rem;
    color: var(--aw-color-text-muted);
    margin: 0.5rem 0 0;
    line-height: 1.5;
  }
  
  .promo-image {
    flex: 1;
    min-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: color-mix(in srgb, var(--aw-color-bg-page) 98%, var(--aw-color-primary));
    padding: 1rem;
  }
  
  .promo-image img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  @media (max-width: 768px) {
    .promo-banner {
      flex-direction: column;
    }
    
    .promo-content, 
    .promo-image {
      min-width: 100%;
    }
    
    .promo-image {
      order: -1;
      padding: 1.5rem;
    }
  }
</style>
