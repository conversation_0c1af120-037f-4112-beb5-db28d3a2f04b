---
import type { ItemGrid as Props } from '~/types';
import { Icon } from 'astro-icon/components';
import { twMerge } from 'tailwind-merge';
import Button from './Button.astro';

const { items = [], columns, defaultIcon = '', classes = {} } = Astro.props;

const {
  container: containerClass = '',
  // container: containerClass = "sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  panel: panelClass = '',
  title: titleClass = '',
  description: descriptionClass = '',
  icon: defaultIconClass = 'text-primary',
} = classes;
---

{
  items && items.length > 0 && (
    <div
      class={twMerge(
        `grid gap-8 gap-x-12 sm:gap-y-8 ${
          columns === 4
            ? 'lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2'
            : columns === 3
              ? 'lg:grid-cols-3 sm:grid-cols-2'
              : columns === 2
                ? 'sm:grid-cols-2 '
                : ''
        }`,
        containerClass
      )}
    >
      {items.map(({ title, description, icon, callToAction, classes: itemClasses = {} }) => (
        <div
          class={twMerge(
            'relative flex flex-col intersect-once intersect-quarter intercept-no-queue motion-safe:md:opacity-0 motion-safe:md:intersect:animate-fade',
            panelClass,
            itemClasses?.panel
          )}
        >
          {(icon || defaultIcon) && (
            <Icon name={icon || defaultIcon} class={twMerge('mb-2 w-10 h-10', defaultIconClass, itemClasses?.icon)} />
          )}
          <div class={twMerge('text-xl font-bold', titleClass, itemClasses?.title)}>{title}</div>
          {description && (
            <p class={twMerge('text-muted mt-2', descriptionClass, itemClasses?.description)} set:html={description} />
          )}
          {callToAction && (
            <div class="mt-2">
              <Button {...callToAction} />
            </div>
          )}
        </div>
      ))}
    </div>
  )
}
