---
description: 
globs: 
alwaysApply: false
---
# Guide: Converting Markdown to MDX and SEO Optimization

This guide outlines the process for converting existing Markdown (`.md`) blog posts to MDX (`.mdx`) format and optimizing them for Search Engines, based on the workflow used in this project.

**Goal:** To have blog post files in `.mdx` format with comprehensive frontmatter for better data management and SEO, and well-structured content.

**Steps:**

1.  **Rename the file:** Change the file extension from `.md` to `.mdx`. (e.g., `biaya-maintenance-website.md` becomes `biaya-maintenance-website.mdx`).

2.  **Update Frontmatter:** Add or update the YAML frontmatter at the top of the file. Ensure it includes:
    *   `title`: The main title of the post.
    *   `date`: Publication date.
    *   `categories`: Relevant categories (as an array).
    *   `image`: URL of the featured image (e.g., `https://img.penasihathosting.com/2025/May/featured-image.webp`).
    *   `excerpt`: A brief summary of the post content.
    *   `metadata`: An object containing SEO-specific tags:
        *   `title`: An SEO-optimized title (around 50-60 characters, including primary keywords).
        *   `description`: An SEO-optimized meta description (around 150-160 characters, summarizing content and including keywords).
    Refer to the SEO Best Practices rule ([mdc:seo-title-description-best-practices.mdc](mdc:seo-title-description-best-practices.mdc)) for guidance on writing effective titles and descriptions.
    Example frontmatter:
    ```yaml
    ---
    title: "Judul Artikel Anda"
    date: YYYY-MM-DD
    categories:
      - "kategori"
    image: https://img.penasihathosting.com/path/to/image.webp
    excerpt: "Ringkasan singkat artikel Anda."
    metadata:
      title: "Judul SEO yang Dioptimalkan"
      description: "Deskripsi meta yang menarik dan informatif."
    ---
    ```

3.  **Optimize Images:** For all images in the post content, ensure they have descriptive `alt` text. This improves accessibility and image SEO.
    Format: `![Deskripsi alt text gambar](mdc:URL_gambar)`

4.  **Standardize Content Formatting:** Replace any custom or non-standard formatting used in the original Markdown file with standard Markdown or MDX syntax.
    *   Replace custom step markers (like `one-number-round`) with standard Markdown headings (e.g., `### Langkah 1`).
    *   Replace custom table placeholders (like `[table id=92 /]`) with standard Markdown tables.

By following these steps, you can convert your existing `.md` files to `.mdx` and ensure they are well-prepared for rendering in the AstroWind project with improved SEO.
