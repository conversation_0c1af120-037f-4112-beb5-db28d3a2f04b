{"name": "orbital-osiris", "version": "1.0.0-beta.52", "description": "AstroWind: A free template using Astro 5.0 and Tailwind CSS. Astro starter theme.", "type": "module", "engines": {"node": "^18.17.1 || ^20.3.0 || >= 21.0.0"}, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "check": "npm run check:astro && npm run check:eslint && npm run check:prettier", "check:astro": "astro check", "check:eslint": "eslint .", "check:prettier": "prettier --check .", "fix": "npm run fix:eslint && npm run fix:prettier", "fix:eslint": "eslint --fix .", "fix:prettier": "prettier -w ."}, "dependencies": {"@astrojs/rss": "^4.0.11", "@astrojs/sitemap": "^3.3.0", "@astrolib/analytics": "^0.6.1", "@astrolib/seo": "^1.0.0-beta.8", "@fontsource-variable/inter": "^5.2.5", "@fontsource/dm-sans": "^5.2.5", "@fontsource/fira-sans": "^5.2.5", "astro": "^5.7.3", "astro-embed": "^0.9.0", "astro-icon": "^1.1.5", "limax": "4.1.0", "lodash.merge": "^4.6.2", "unpic": "^4.1.2"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.2.4", "@astrojs/partytown": "^2.1.4", "@astrojs/tailwind": "^5.1.5", "@eslint/js": "^9.24.0", "@iconify-json/flat-color-icons": "^1.2.1", "@iconify-json/tabler": "^1.2.17", "@tailwindcss/typography": "^0.5.16", "@types/js-yaml": "^4.0.9", "@types/lodash.merge": "^4.6.9", "@types/mdx": "^2.0.13", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "astro-compress": "2.3.8", "astro-eslint-parser": "^1.2.2", "eslint": "^9.24.0", "eslint-plugin-astro": "^1.3.1", "globals": "^16.0.0", "js-yaml": "^4.1.0", "mdast-util-to-string": "^4.0.0", "prettier": "^3.5.3", "prettier-plugin-astro": "^0.14.1", "sharp": "0.34.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "unist-util-visit": "^5.0.0"}}